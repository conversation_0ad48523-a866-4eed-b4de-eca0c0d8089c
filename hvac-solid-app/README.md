# HVAC Control Panel

A modern, high-performance web application for monitoring and controlling HVAC systems, built with SolidJS and TypeScript.

## ✨ Features

- Real-time monitoring of HVAC system status
- Interactive temperature and fan speed controls
- Responsive design for all devices
- Secure authentication
- Optimized for performance and low resource usage

## 🚀 Getting Started

### Prerequisites

- Node.js 16+ and npm 8+
- Go backend API (see backend setup)

### Installation

1. Clone the repository
2. Install dependencies:

```bash
npm install
```

3. Configure environment variables:
   - Copy `.env.example` to `.env`
   - Update the API base URL if needed

### Development

Start the development server:

```bash
npm run dev
```

The application will be available at [http://localhost:5173](http://localhost:5173)

### Building for Production

To create a production build:

```bash
npm run build
```

## 🛠️ Project Structure

```
src/
├── features/           # Feature-based modules
│   ├── auth/          # Authentication logic
│   └── dashboard/     # Main dashboard components
├── lib/               # Shared utilities and services
│   └── api/           # API client and services
└── components/        # Reusable UI components
```

## 🔧 Configuration

Update the `.env` file to configure:

- `VITE_API_BASE_URL`: Your backend API URL (default: http://localhost:8080)
- `VITE_ENV`: Environment (development/production)

## 📦 Dependencies

- [SolidJS](https://www.solidjs.com/) - A declarative, efficient, and flexible JavaScript library for building user interfaces.
- [Vite](https://vitejs.dev/) - Next Generation Frontend Tooling
- [Tailwind CSS](https://tailwindcss.com/) - A utility-first CSS framework

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🌟 Acknowledgements

- [SolidJS Documentation](https://www.solidjs.com/)
- [Vite Documentation](https://vitejs.dev/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
