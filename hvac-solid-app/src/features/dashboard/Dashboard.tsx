import { createEffect, createSignal, onMount } from 'solid-js';
import { useNavigate } from '@solidjs/router';
import { apiClient } from '../../lib/api/client';
import { useAuth } from '../auth/AuthProvider';

type HVACData = {
  temperature: number;
  humidity: number;
  pressure: number;
  lastUpdated: string;
  status: 'idle' | 'cooling' | 'heating' | 'error';
};

export default function Dashboard() {
  const auth = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = createSignal(true);
  const [data, setData] = createSignal<HVACData | null>(null);
  const [error, setError] = createSignal<string | null>(null);

  const fetchHVACData = async () => {
    try {
      setLoading(true);
      const response = await apiClient.get('/api/v1/hvac/status');
      setData(response.data);
      setError(null);
    } catch (err) {
      console.error('Failed to fetch HVAC data:', err);
      setError('Failed to load HVAC data');
    } finally {
      setLoading(false);
    }
  };

  onMount(() => {
    if (!auth.isAuthenticated()) {
      navigate('/login');
      return;
    }
    fetchHVACData();
    
    // Set up polling
    const interval = setInterval(fetchHVACData, 30000); // Poll every 30 seconds
    
    return () => clearInterval(interval);
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'cooling': return 'text-blue-500';
      case 'heating': return 'text-red-500';
      case 'error': return 'text-red-700';
      default: return 'text-green-500';
    }
  };

  return (
    <div class="p-6">
      <h1 class="text-2xl font-bold mb-6">HVAC System Dashboard</h1>
      
      {loading() && !data() ? (
        <div>Loading...</div>
      ) : error() ? (
        <div class="text-red-500">{error()}</div>
      ) : data() ? (
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-gray-500 text-sm font-medium">Temperature</h3>
            <p class="text-3xl font-bold">{data()?.temperature}°C</p>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-gray-500 text-sm font-medium">Humidity</h3>
            <p class="text-3xl font-bold">{data()?.humidity}%</p>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-gray-500 text-sm font-medium">Pressure</h3>
            <p class="text-3xl font-bold">{data()?.pressure} hPa</p>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-gray-500 text-sm font-medium">Status</h3>
            <p class={`text-2xl font-bold ${getStatusColor(data()?.status || '')}`}>
              {data()?.status.toUpperCase()}
            </p>
          </div>
          
          <div class="md:col-span-2 bg-white p-6 rounded-lg shadow">
            <h3 class="text-gray-500 text-sm font-medium">Last Updated</h3>
            <p class="text-lg">
              {new Date(data()?.lastUpdated || '').toLocaleString()}
            </p>
          </div>
        </div>
      ) : (
        <div>No data available</div>
      )}
      
      <div class="mt-8">
        <button 
          onclick={() => auth.logout()} 
          class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
        >
          Logout
        </button>
      </div>
    </div>
  );
}
