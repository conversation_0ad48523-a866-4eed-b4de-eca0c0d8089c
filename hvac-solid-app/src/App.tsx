import { Router, Route } from '@solidjs/router';
import { lazy, onMount } from 'solid-js';
import { AuthProvider, useAuth } from './features/auth/AuthProvider';
import './App.css';

// Lazy load components
const Login = lazy(() => import('./features/auth/Login'));
const Dashboard = lazy(() => import('./features/dashboard/Dashboard'));

function App() {
  const auth = useAuth();
  
  // Check for existing session on app load
  onMount(() => {
    // Redirect to login if not authenticated and not on login page
    if (!auth.isAuthenticated() && window.location.pathname !== '/login') {
      window.location.href = '/login';
    }
  });

  return (
    <div class="min-h-screen bg-gray-100">
      <Router>
        <Route path="/login" component={Login} />
        <Route path="/dashboard" component={Dashboard} />
        <Route path="/" component={() => <Dashboard />} />
        <Route path="*" component={() => <Dashboard />} />
      </Router>
    </div>
  );
}

// Wrap the app with AuthProvider
const AppWithProviders = () => (
  <AuthProvider>
    <App />
  </AuthProvider>
);

export default AppWithProviders;
