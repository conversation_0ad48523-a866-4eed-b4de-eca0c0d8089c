import { DefaultError, Query<PERSON><PERSON>, Omit<PERSON>eyof, InfiniteQueryObserverOptions as InfiniteQueryObserverOptions$1, QueryClient as QueryClient$1, QueryClientConfig as QueryClientConfig$1, DefaultOptions as DefaultOptions$1, QueryObserverOptions as QueryObserverOptions$1, DefinedInfiniteQueryObserverResult, InfiniteQueryObserverResult, QueryObserverResult, DefinedQueryObserverResult, MutationObserverOptions, MutateFunction, Override, MutationObserverResult, DataTag, InfiniteData, NonUndefinedGuard, QueryFunction, ThrowOnError, QueriesPlaceholderDataFunction, QueryFilters, MutationFilters, MutationState, Mutation } from '@tanstack/query-core';
export * from '@tanstack/query-core';
import * as solid_js from 'solid-js';
import { Accessor, JSX } from 'solid-js';

interface QueryObserverOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryData = TQueryFnData, TQueryKey extends QueryKey = QueryKey, TPageParam = never> extends OmitKeyof<QueryObserverOptions$1<TQueryFnData, TError, TData, TQueryData, TQueryKey, TPageParam>, 'structuralSharing'> {
    /**
     * Set this to a reconciliation key to enable reconciliation between query results.
     * Set this to `false` to disable reconciliation between query results.
     * Set this to a function which accepts the old and new data and returns resolved data of the same type to implement custom reconciliation logic.
     * Defaults reconciliation to false.
     */
    reconcile?: string | false | ((oldData: TData | undefined, newData: TData) => TData);
}
interface InfiniteQueryObserverOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryData = TQueryFnData, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown> extends OmitKeyof<InfiniteQueryObserverOptions$1<TQueryFnData, TError, TData, TQueryData, TQueryKey, TPageParam>, 'structuralSharing'> {
    /**
     * Set this to a reconciliation key to enable reconciliation between query results.
     * Set this to `false` to disable reconciliation between query results.
     * Set this to a function which accepts the old and new data and returns resolved data of the same type to implement custom reconciliation logic.
     * Defaults reconciliation to false.
     */
    reconcile?: string | false | ((oldData: TData | undefined, newData: TData) => TData);
}
interface DefaultOptions<TError = DefaultError> extends DefaultOptions$1<TError> {
    queries?: OmitKeyof<QueryObserverOptions<unknown, TError>, 'queryKey'>;
}
interface QueryClientConfig extends QueryClientConfig$1 {
    defaultOptions?: DefaultOptions;
}
declare class QueryClient extends QueryClient$1 {
    constructor(config?: QueryClientConfig);
}

interface UseBaseQueryOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryData = TQueryFnData, TQueryKey extends QueryKey = QueryKey> extends OmitKeyof<QueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey>, 'suspense'> {
    /**
     * Only applicable while rendering queries on the server with streaming.
     * Set `deferStream` to `true` to wait for the query to resolve on the server before flushing the stream.
     * This can be useful to avoid sending a loading state to the client before the query has resolved.
     * Defaults to `false`.
     */
    deferStream?: boolean;
    /**
     * @deprecated The `suspense` option has been deprecated in v5 and will be removed in the next major version.
     * The `data` property on useQuery is a SolidJS resource and will automatically suspend when the data is loading.
     * Setting `suspense` to `false` will be a no-op.
     */
    suspense?: boolean;
}
interface SolidQueryOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey> extends UseBaseQueryOptions<TQueryFnData, TError, TData, TQueryFnData, TQueryKey> {
}
type UseQueryOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey> = Accessor<SolidQueryOptions<TQueryFnData, TError, TData, TQueryKey>>;
type UseBaseQueryResult<TData = unknown, TError = DefaultError> = QueryObserverResult<TData, TError>;
type UseQueryResult<TData = unknown, TError = DefaultError> = UseBaseQueryResult<TData, TError>;
type DefinedUseBaseQueryResult<TData = unknown, TError = DefaultError> = DefinedQueryObserverResult<TData, TError>;
type DefinedUseQueryResult<TData = unknown, TError = DefaultError> = DefinedUseBaseQueryResult<TData, TError>;
interface SolidInfiniteQueryOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryData = TQueryFnData, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown> extends OmitKeyof<InfiniteQueryObserverOptions<TQueryFnData, TError, TData, TQueryData, TQueryKey, TPageParam>, 'queryKey' | 'suspense'> {
    queryKey: TQueryKey;
    /**
     * Only applicable while rendering queries on the server with streaming.
     * Set `deferStream` to `true` to wait for the query to resolve on the server before flushing the stream.
     * This can be useful to avoid sending a loading state to the client before the query has resolved.
     * Defaults to `false`.
     */
    deferStream?: boolean;
    /**
     * @deprecated The `suspense` option has been deprecated in v5 and will be removed in the next major version.
     * The `data` property on useInfiniteQuery is a SolidJS resource and will automatically suspend when the data is loading.
     * Setting `suspense` to `false` will be a no-op.
     */
    suspense?: boolean;
}
type UseInfiniteQueryOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown> = Accessor<SolidInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryFnData, TQueryKey, TPageParam>>;
type UseInfiniteQueryResult<TData = unknown, TError = DefaultError> = InfiniteQueryObserverResult<TData, TError>;
type DefinedUseInfiniteQueryResult<TData = unknown, TError = DefaultError> = DefinedInfiniteQueryObserverResult<TData, TError>;
interface SolidMutationOptions<TData = unknown, TError = DefaultError, TVariables = void, TContext = unknown> extends OmitKeyof<MutationObserverOptions<TData, TError, TVariables, TContext>, '_defaulted'> {
}
type UseMutationOptions<TData = unknown, TError = DefaultError, TVariables = void, TContext = unknown> = Accessor<SolidMutationOptions<TData, TError, TVariables, TContext>>;
type UseMutateFunction<TData = unknown, TError = DefaultError, TVariables = void, TContext = unknown> = (...args: Parameters<MutateFunction<TData, TError, TVariables, TContext>>) => void;
type UseMutateAsyncFunction<TData = unknown, TError = DefaultError, TVariables = void, TContext = unknown> = MutateFunction<TData, TError, TVariables, TContext>;
type UseBaseMutationResult<TData = unknown, TError = DefaultError, TVariables = unknown, TContext = unknown> = Override<MutationObserverResult<TData, TError, TVariables, TContext>, {
    mutate: UseMutateFunction<TData, TError, TVariables, TContext>;
}> & {
    mutateAsync: UseMutateAsyncFunction<TData, TError, TVariables, TContext>;
};
type UseMutationResult<TData = unknown, TError = DefaultError, TVariables = unknown, TContext = unknown> = UseBaseMutationResult<TData, TError, TVariables, TContext>;

type UndefinedInitialDataOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey> = Accessor<SolidQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {
    initialData?: undefined;
}>;
type DefinedInitialDataOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey> = Accessor<SolidQueryOptions<TQueryFnData, TError, TData, TQueryKey> & {
    initialData: TQueryFnData | (() => TQueryFnData);
}>;
declare function queryOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey, TOptions extends ReturnType<UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>> = ReturnType<UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>>>(options: ReturnType<UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>>): ReturnType<UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>> & {
    queryKey: DataTag<TQueryKey, TQueryFnData>;
};
declare function queryOptions<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey, TOptions extends ReturnType<DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>> = ReturnType<DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>>>(options: ReturnType<DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>>): ReturnType<DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>> & {
    queryKey: DataTag<TQueryKey, TQueryFnData>;
};

declare function useQuery<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey>(options: UndefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>, queryClient?: () => QueryClient): UseQueryResult<TData, TError>;
declare function useQuery<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey>(options: DefinedInitialDataOptions<TQueryFnData, TError, TData, TQueryKey>, queryClient?: () => QueryClient): DefinedUseQueryResult<TData, TError>;

type UndefinedInitialDataInfiniteOptions<TQueryFnData, TError = DefaultError, TData = InfiniteData<TQueryFnData>, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown> = Accessor<SolidInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryFnData, TQueryKey, TPageParam> & {
    initialData?: undefined;
}>;
type DefinedInitialDataInfiniteOptions<TQueryFnData, TError = DefaultError, TData = InfiniteData<TQueryFnData>, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown> = Accessor<SolidInfiniteQueryOptions<TQueryFnData, TError, TData, TQueryFnData, TQueryKey, TPageParam> & {
    initialData: NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>> | (() => NonUndefinedGuard<InfiniteData<TQueryFnData, TPageParam>>);
}>;
declare function infiniteQueryOptions<TQueryFnData, TError = DefaultError, TData = InfiniteData<TQueryFnData>, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown>(options: ReturnType<DefinedInitialDataInfiniteOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>>): ReturnType<DefinedInitialDataInfiniteOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>> & {
    queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>>;
};
declare function infiniteQueryOptions<TQueryFnData, TError = DefaultError, TData = InfiniteData<TQueryFnData>, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown>(options: ReturnType<UndefinedInitialDataInfiniteOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>>): ReturnType<UndefinedInitialDataInfiniteOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>> & {
    queryKey: DataTag<TQueryKey, InfiniteData<TQueryFnData>>;
};

declare function useInfiniteQuery<TQueryFnData, TError = DefaultError, TData = InfiniteData<TQueryFnData>, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown>(options: DefinedInitialDataInfiniteOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>, queryClient?: Accessor<QueryClient>): DefinedUseInfiniteQueryResult<TData, TError>;
declare function useInfiniteQuery<TQueryFnData, TError = DefaultError, TData = InfiniteData<TQueryFnData>, TQueryKey extends QueryKey = QueryKey, TPageParam = unknown>(options: UndefinedInitialDataInfiniteOptions<TQueryFnData, TError, TData, TQueryKey, TPageParam>, queryClient?: Accessor<QueryClient>): UseInfiniteQueryResult<TData, TError>;

declare function useMutation<TData = unknown, TError = DefaultError, TVariables = void, TContext = unknown>(options: UseMutationOptions<TData, TError, TVariables, TContext>, queryClient?: Accessor<QueryClient>): UseMutationResult<TData, TError, TVariables, TContext>;

type UseQueryOptionsForUseQueries<TQueryFnData = unknown, TError = DefaultError, TData = TQueryFnData, TQueryKey extends QueryKey = QueryKey> = OmitKeyof<SolidQueryOptions<TQueryFnData, TError, TData, TQueryKey>, 'placeholderData' | 'suspense'> & {
    placeholderData?: TQueryFnData | QueriesPlaceholderDataFunction<TQueryFnData>;
    /**
     * @deprecated The `suspense` option has been deprecated in v5 and will be removed in the next major version.
     * The `data` property on useQueries is a plain object and not a SolidJS Resource.
     * It will not suspend when the data is loading.
     * Setting `suspense` to `true` will be a no-op.
     */
    suspense?: boolean;
};
type MAXIMUM_DEPTH = 20;
type SkipTokenForUseQueries = symbol;
type GetOptions<T> = T extends {
    queryFnData: infer TQueryFnData;
    error?: infer TError;
    data: infer TData;
} ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData> : T extends {
    queryFnData: infer TQueryFnData;
    error?: infer TError;
} ? UseQueryOptionsForUseQueries<TQueryFnData, TError> : T extends {
    data: infer TData;
    error?: infer TError;
} ? UseQueryOptionsForUseQueries<unknown, TError, TData> : T extends [infer TQueryFnData, infer TError, infer TData] ? UseQueryOptionsForUseQueries<TQueryFnData, TError, TData> : T extends [infer TQueryFnData, infer TError] ? UseQueryOptionsForUseQueries<TQueryFnData, TError> : T extends [infer TQueryFnData] ? UseQueryOptionsForUseQueries<TQueryFnData> : T extends {
    queryFn?: QueryFunction<infer TQueryFnData, infer TQueryKey> | SkipTokenForUseQueries;
    select?: (data: any) => infer TData;
    throwOnError?: ThrowOnError<any, infer TError, any, any>;
} ? UseQueryOptionsForUseQueries<TQueryFnData, unknown extends TError ? DefaultError : TError, unknown extends TData ? TQueryFnData : TData, TQueryKey> : UseQueryOptionsForUseQueries;
type GetResults<T> = T extends {
    queryFnData: any;
    error?: infer TError;
    data: infer TData;
} ? UseQueryResult<TData, TError> : T extends {
    queryFnData: infer TQueryFnData;
    error?: infer TError;
} ? UseQueryResult<TQueryFnData, TError> : T extends {
    data: infer TData;
    error?: infer TError;
} ? UseQueryResult<TData, TError> : T extends [any, infer TError, infer TData] ? UseQueryResult<TData, TError> : T extends [infer TQueryFnData, infer TError] ? UseQueryResult<TQueryFnData, TError> : T extends [infer TQueryFnData] ? UseQueryResult<TQueryFnData> : T extends {
    queryFn?: QueryFunction<infer TQueryFnData, any> | SkipTokenForUseQueries;
    select?: (data: any) => infer TData;
    throwOnError?: ThrowOnError<any, infer TError, any, any>;
} ? UseQueryResult<unknown extends TData ? TQueryFnData : TData, unknown extends TError ? DefaultError : TError> : UseQueryResult;
/**
 * QueriesOptions reducer recursively unwraps function arguments to infer/enforce type param
 */
type QueriesOptions<T extends Array<any>, TResult extends Array<any> = [], TDepth extends ReadonlyArray<number> = []> = TDepth['length'] extends MAXIMUM_DEPTH ? Array<UseQueryOptionsForUseQueries> : T extends [] ? [] : T extends [infer Head] ? [...TResult, GetOptions<Head>] : T extends [infer Head, ...infer Tail] ? QueriesOptions<[
    ...Tail
], [
    ...TResult,
    GetOptions<Head>
], [
    ...TDepth,
    1
]> : ReadonlyArray<unknown> extends T ? T : T extends Array<UseQueryOptionsForUseQueries<infer TQueryFnData, infer TError, infer TData, infer TQueryKey>> ? Array<UseQueryOptionsForUseQueries<TQueryFnData, TError, TData, TQueryKey>> : Array<UseQueryOptionsForUseQueries>;
/**
 * QueriesResults reducer recursively maps type param to results
 */
type QueriesResults<T extends Array<any>, TResult extends Array<any> = [], TDepth extends ReadonlyArray<number> = []> = TDepth['length'] extends MAXIMUM_DEPTH ? Array<UseQueryResult> : T extends [] ? [] : T extends [infer Head] ? [...TResult, GetResults<Head>] : T extends [infer Head, ...infer Tail] ? QueriesResults<[
    ...Tail
], [
    ...TResult,
    GetResults<Head>
], [
    ...TDepth,
    1
]> : {
    [K in keyof T]: GetResults<T[K]>;
};
declare function useQueries<T extends Array<any>, TCombinedResult extends QueriesResults<T> = QueriesResults<T>>(queriesOptions: Accessor<{
    queries: readonly [...QueriesOptions<T>] | readonly [...{
        [K in keyof T]: GetOptions<T[K]>;
    }];
    combine?: (result: QueriesResults<T>) => TCombinedResult;
}>, queryClient?: Accessor<QueryClient>): TCombinedResult;

declare const QueryClientContext: solid_js.Context<(() => QueryClient) | undefined>;
declare const useQueryClient: (queryClient?: QueryClient) => QueryClient;
type QueryClientProviderProps = {
    client: QueryClient;
    children?: JSX.Element;
};
declare const QueryClientProvider: (props: QueryClientProviderProps) => JSX.Element;

declare function useIsFetching(filters?: Accessor<QueryFilters>, queryClient?: Accessor<QueryClient>): Accessor<number>;

declare function useIsMutating(filters?: Accessor<MutationFilters>, queryClient?: Accessor<QueryClient>): Accessor<number>;

type MutationStateOptions<TResult = MutationState> = {
    filters?: MutationFilters;
    select?: (mutation: Mutation) => TResult;
};
declare function useMutationState<TResult = MutationState>(options?: Accessor<MutationStateOptions<TResult>>, queryClient?: Accessor<QueryClient>): Accessor<Array<TResult>>;

declare const useIsRestoring: () => Accessor<boolean>;
declare const IsRestoringProvider: solid_js.ContextProviderComponent<Accessor<boolean>>;

/** @deprecated Use UseQueryOptions instead */
type CreateQueryOptions = UseQueryOptions;
/** @deprecated Use UseBaseQueryResult instead */
type CreateBaseQueryResult = UseBaseQueryResult;
/** @deprecated Use UseQueryResult instead */
type CreateQueryResult = UseQueryResult;
/** @deprecated Use DefinedUseBaseQueryResult instead */
type DefinedCreateBaseQueryResult = DefinedUseBaseQueryResult;
/** @deprecated Use DefinedUseQueryResult instead */
type DefinedCreateQueryResult = DefinedUseQueryResult;
/** @deprecated Use UseInfiniteQueryOptions instead */
type CreateInfiniteQueryOptions = UseInfiniteQueryOptions;
/** @deprecated Use UseInfiniteQueryResult instead */
type CreateInfiniteQueryResult = UseInfiniteQueryResult;
/** @deprecated Use DefinedUseInfiniteQueryResult instead */
type DefinedCreateInfiniteQueryResult = DefinedUseInfiniteQueryResult;
/** @deprecated Use UseMutationOptions instead */
type CreateMutationOptions = UseMutationOptions;
/** @deprecated Use UseMutateFunction instead */
type CreateMutateFunction = UseMutateFunction;
/** @deprecated Use UseMutateAsyncFunction instead */
type CreateMutateAsyncFunction = UseMutateAsyncFunction;
/** @deprecated Use UseBaseMutationResult instead */
type CreateBaseMutationResult = UseBaseMutationResult;
/** @deprecated Use UseMutationResult instead */
type CreateMutationResult = UseMutationResult;
/** @deprecated Use UseBaseQueryOptions instead */
type CreateBaseQueryOptions = UseBaseQueryOptions;

/** @deprecated Use useQuery instead */
declare const createQuery: typeof useQuery;

/** @deprecated Use useInfiniteQuery instead */
declare const createInfiniteQuery: typeof useInfiniteQuery;

/** @deprecated Use useMutation instead */
declare const createMutation: typeof useMutation;

/** @deprecated Use useQueries instead */
declare const createQueries: typeof useQueries;

export { type CreateBaseMutationResult, type CreateBaseQueryOptions, type CreateBaseQueryResult, type CreateInfiniteQueryOptions, type CreateInfiniteQueryResult, type CreateMutateAsyncFunction, type CreateMutateFunction, type CreateMutationOptions, type CreateMutationResult, type CreateQueryOptions, type CreateQueryResult, type DefaultOptions, type DefinedCreateBaseQueryResult, type DefinedCreateInfiniteQueryResult, type DefinedCreateQueryResult, type DefinedInitialDataInfiniteOptions, type DefinedInitialDataOptions, type DefinedUseBaseQueryResult, type DefinedUseInfiniteQueryResult, type DefinedUseQueryResult, type InfiniteQueryObserverOptions, IsRestoringProvider, QueryClient, type QueryClientConfig, QueryClientContext, QueryClientProvider, type QueryClientProviderProps, type QueryObserverOptions, type SolidInfiniteQueryOptions, type SolidMutationOptions, type SolidQueryOptions, type UndefinedInitialDataInfiniteOptions, type UndefinedInitialDataOptions, type UseBaseMutationResult, type UseBaseQueryOptions, type UseBaseQueryResult, type UseInfiniteQueryOptions, type UseInfiniteQueryResult, type UseMutateAsyncFunction, type UseMutateFunction, type UseMutationOptions, type UseMutationResult, type UseQueryOptions, type UseQueryResult, createInfiniteQuery, createMutation, createQueries, createQuery, infiniteQueryOptions, queryOptions, useInfiniteQuery, useIsFetching, useIsMutating, useIsRestoring, useMutation, useMutationState, useQueries, useQuery, useQueryClient };
