'use strict';

var queryCore = require('@tanstack/query-core');
var solidJs = require('solid-js');
var web = require('solid-js/web');
var store = require('solid-js/store');

// src/useQuery.ts
exports.QueryClientContext = solidJs.createContext(void 0);
exports.useQueryClient = (queryClient) => {
  if (queryClient) {
    return queryClient;
  }
  const client = solidJs.useContext(exports.QueryClientContext);
  if (!client) {
    throw new Error("No QueryClient set, use QueryClientProvider to set one");
  }
  return client();
};
exports.QueryClientProvider = (props) => {
  solidJs.createRenderEffect((unmount) => {
    unmount?.();
    props.client.mount();
    return props.client.unmount.bind(props.client);
  });
  solidJs.onCleanup(() => props.client.unmount());
  return web.createComponent(exports.QueryClientContext.Provider, {
    value: () => props.client,
    get children() {
      return props.children;
    }
  });
};
var IsRestoringContext = solidJs.createContext(() => false);
exports.useIsRestoring = () => solidJs.useContext(IsRestoringContext);
exports.IsRestoringProvider = IsRestoringContext.Provider;

// src/useBaseQuery.ts
function reconcileFn(store$1, result, reconcileOption, queryHash) {
  if (reconcileOption === false) return result;
  if (typeof reconcileOption === "function") {
    const newData2 = reconcileOption(store$1.data, result.data);
    return { ...result, data: newData2 };
  }
  let data = result.data;
  if (store$1.data === void 0) {
    try {
      data = structuredClone(data);
    } catch (error) {
      {
        if (error instanceof Error) {
          console.warn(
            `Unable to correctly reconcile data for query key: ${queryHash}. Possibly because the query data contains data structures that aren't supported by the 'structuredClone' algorithm. Consider using a callback function instead to manage the reconciliation manually.

 Error Received: ${error.name} - ${error.message}`
          );
        }
      }
    }
  }
  const newData = store.reconcile(data, { key: reconcileOption })(store$1.data);
  return { ...result, data: newData };
}
var hydratableObserverResult = (query, result) => {
  if (!web.isServer) return result;
  const obj = {
    ...store.unwrap(result),
    // During SSR, functions cannot be serialized, so we need to remove them
    // This is safe because we will add these functions back when the query is hydrated
    refetch: void 0
  };
  if ("fetchNextPage" in result) {
    obj.fetchNextPage = void 0;
    obj.fetchPreviousPage = void 0;
  }
  obj.hydrationData = {
    state: query.state,
    queryKey: query.queryKey,
    queryHash: query.queryHash,
    ...query.meta && { meta: query.meta }
  };
  return obj;
};
function useBaseQuery(options, Observer, queryClient) {
  const client = solidJs.createMemo(() => exports.useQueryClient(queryClient?.()));
  const isRestoring = exports.useIsRestoring();
  let unsubscribeQueued = false;
  const defaultedOptions = solidJs.createMemo(() => {
    const defaultOptions = client().defaultQueryOptions(options());
    defaultOptions._optimisticResults = isRestoring() ? "isRestoring" : "optimistic";
    defaultOptions.structuralSharing = false;
    if (web.isServer) {
      defaultOptions.retry = false;
      defaultOptions.throwOnError = true;
    }
    return defaultOptions;
  });
  const initialOptions = defaultedOptions();
  const [observer, setObserver] = solidJs.createSignal(
    new Observer(client(), defaultedOptions())
  );
  let observerResult = observer().getOptimisticResult(defaultedOptions());
  const [state, setState] = store.createStore(observerResult);
  const createServerSubscriber = (resolve, reject) => {
    return observer().subscribe((result) => {
      queryCore.notifyManager.batchCalls(() => {
        const query = observer().getCurrentQuery();
        const unwrappedResult = hydratableObserverResult(query, result);
        if (unwrappedResult.isError) {
          reject(unwrappedResult.error);
          unsubscribeIfQueued();
        } else {
          resolve(unwrappedResult);
          unsubscribeIfQueued();
        }
      })();
    });
  };
  const unsubscribeIfQueued = () => {
    if (unsubscribeQueued) {
      unsubscribe?.();
      unsubscribeQueued = false;
    }
  };
  const createClientSubscriber = () => {
    const obs = observer();
    return obs.subscribe((result) => {
      observerResult = result;
      queueMicrotask(() => {
        if (unsubscribe) {
          refetch();
        }
      });
    });
  };
  function setStateWithReconciliation(res) {
    const opts = observer().options;
    const reconcileOptions = opts.reconcile;
    setState((store) => {
      return reconcileFn(
        store,
        res,
        reconcileOptions === void 0 ? false : reconcileOptions,
        opts.queryHash
      );
    });
  }
  function createDeepSignal() {
    return [
      () => state,
      (v) => {
        const unwrapped = store.unwrap(state);
        if (typeof v === "function") {
          v = v(unwrapped);
        }
        if (v?.hydrationData) {
          const { hydrationData, ...rest } = v;
          v = rest;
        }
        setStateWithReconciliation(v);
      }
    ];
  }
  let unsubscribe = null;
  let resolver = null;
  const [queryResource, { refetch }] = solidJs.createResource(
    () => {
      const obs = observer();
      return new Promise((resolve, reject) => {
        resolver = resolve;
        if (web.isServer) {
          unsubscribe = createServerSubscriber(resolve, reject);
        } else if (!unsubscribe && !isRestoring()) {
          unsubscribe = createClientSubscriber();
        }
        obs.updateResult();
        if (observerResult.isError && !observerResult.isFetching && !isRestoring() && queryCore.shouldThrowError(obs.options.throwOnError, [
          observerResult.error,
          obs.getCurrentQuery()
        ])) {
          setStateWithReconciliation(observerResult);
          return reject(observerResult.error);
        }
        if (!observerResult.isLoading) {
          resolver = null;
          return resolve(
            hydratableObserverResult(obs.getCurrentQuery(), observerResult)
          );
        }
        setStateWithReconciliation(observerResult);
      });
    },
    {
      storage: createDeepSignal,
      get deferStream() {
        return options().deferStream;
      },
      /**
       * If this resource was populated on the server (either sync render, or streamed in over time), onHydrated
       * will be called. This is the point at which we can hydrate the query cache state, and setup the query subscriber.
       *
       * Leveraging onHydrated allows us to plug into the async and streaming support that solidjs resources already support.
       *
       * Note that this is only invoked on the client, for queries that were originally run on the server.
       */
      onHydrated(_k, info) {
        if (info.value && "hydrationData" in info.value) {
          queryCore.hydrate(client(), {
            // @ts-expect-error - hydrationData is not correctly typed internally
            queries: [{ ...info.value.hydrationData }]
          });
        }
        if (unsubscribe) return;
        const newOptions = { ...initialOptions };
        if ((initialOptions.staleTime || !initialOptions.initialData) && info.value) {
          newOptions.refetchOnMount = false;
        }
        observer().setOptions(newOptions);
        setStateWithReconciliation(observer().getOptimisticResult(newOptions));
        unsubscribe = createClientSubscriber();
      }
    }
  );
  solidJs.createComputed(
    solidJs.on(
      client,
      (c) => {
        if (unsubscribe) {
          unsubscribe();
        }
        const newObserver = new Observer(c, defaultedOptions());
        unsubscribe = createClientSubscriber();
        setObserver(newObserver);
      },
      {
        defer: true
      }
    )
  );
  solidJs.createComputed(
    solidJs.on(
      isRestoring,
      (restoring) => {
        if (!restoring && !web.isServer) {
          refetch();
        }
      },
      { defer: true }
    )
  );
  solidJs.onCleanup(() => {
    if (web.isServer && queryResource.loading) {
      unsubscribeQueued = true;
      return;
    }
    if (unsubscribe) {
      unsubscribe();
      unsubscribe = null;
    }
    if (resolver && !web.isServer) {
      resolver(observerResult);
      resolver = null;
    }
  });
  solidJs.createComputed(
    solidJs.on(
      [observer, defaultedOptions],
      ([obs, opts]) => {
        obs.setOptions(opts);
        setStateWithReconciliation(obs.getOptimisticResult(opts));
        refetch();
      },
      { defer: true }
    )
  );
  const handler = {
    get(target, prop) {
      if (prop === "data") {
        if (state.data !== void 0) {
          return queryResource.latest?.data;
        }
        return queryResource()?.data;
      }
      return Reflect.get(target, prop);
    }
  };
  return new Proxy(state, handler);
}

// src/useQuery.ts
function useQuery(options, queryClient) {
  return useBaseQuery(
    solidJs.createMemo(() => options()),
    queryCore.QueryObserver,
    queryClient
  );
}
function useInfiniteQuery(options, queryClient) {
  return useBaseQuery(
    solidJs.createMemo(() => options()),
    queryCore.InfiniteQueryObserver,
    queryClient
  );
}

// src/utils.ts
function noop() {
}

// src/useMutation.ts
function useMutation(options, queryClient) {
  const client = solidJs.createMemo(() => exports.useQueryClient(queryClient?.()));
  const observer = new queryCore.MutationObserver(
    client(),
    options()
  );
  const mutate = (variables, mutateOptions) => {
    observer.mutate(variables, mutateOptions).catch(noop);
  };
  const [state, setState] = store.createStore({
    ...observer.getCurrentResult(),
    mutate,
    mutateAsync: observer.getCurrentResult().mutate
  });
  solidJs.createComputed(() => {
    observer.setOptions(options());
  });
  solidJs.createComputed(
    solidJs.on(
      () => state.status,
      () => {
        if (state.isError && queryCore.shouldThrowError(observer.options.throwOnError, [state.error])) {
          throw state.error;
        }
      }
    )
  );
  const unsubscribe = observer.subscribe((result) => {
    setState({
      ...result,
      mutate,
      mutateAsync: result.mutate
    });
  });
  solidJs.onCleanup(unsubscribe);
  return state;
}
function useQueries(queriesOptions, queryClient) {
  const client = solidJs.createMemo(() => exports.useQueryClient(queryClient?.()));
  const isRestoring = exports.useIsRestoring();
  const defaultedQueries = solidJs.createMemo(
    () => queriesOptions().queries.map(
      (options) => solidJs.mergeProps(
        client().defaultQueryOptions(options),
        {
          get _optimisticResults() {
            return isRestoring() ? "isRestoring" : "optimistic";
          }
        }
      )
    )
  );
  const observer = new queryCore.QueriesObserver(
    client(),
    defaultedQueries(),
    queriesOptions().combine ? {
      combine: queriesOptions().combine
    } : void 0
  );
  const [state, setState] = store.createStore(
    observer.getOptimisticResult(
      defaultedQueries(),
      queriesOptions().combine
    )[1]()
  );
  solidJs.createRenderEffect(
    solidJs.on(
      () => queriesOptions().queries.length,
      () => setState(
        observer.getOptimisticResult(
          defaultedQueries(),
          queriesOptions().combine
        )[1]()
      )
    )
  );
  const dataResources = solidJs.createMemo(
    solidJs.on(
      () => state.length,
      () => state.map((queryRes) => {
        const dataPromise = () => new Promise((resolve) => {
          if (queryRes.isFetching && queryRes.isLoading) return;
          resolve(store.unwrap(queryRes.data));
        });
        return solidJs.createResource(dataPromise);
      })
    )
  );
  solidJs.batch(() => {
    const dataResources_ = dataResources();
    for (let index = 0; index < dataResources_.length; index++) {
      const dataResource = dataResources_[index];
      dataResource[1].mutate(() => store.unwrap(state[index].data));
      dataResource[1].refetch();
    }
  });
  let taskQueue = [];
  const subscribeToObserver = () => observer.subscribe((result) => {
    taskQueue.push(() => {
      solidJs.batch(() => {
        const dataResources_ = dataResources();
        for (let index = 0; index < dataResources_.length; index++) {
          const dataResource = dataResources_[index];
          const unwrappedResult = { ...store.unwrap(result[index]) };
          setState(index, store.unwrap(unwrappedResult));
          dataResource[1].mutate(() => store.unwrap(state[index].data));
          dataResource[1].refetch();
        }
      });
    });
    queueMicrotask(() => {
      const taskToRun = taskQueue.pop();
      if (taskToRun) taskToRun();
      taskQueue = [];
    });
  });
  let unsubscribe = noop;
  solidJs.createComputed((cleanup) => {
    cleanup?.();
    unsubscribe = isRestoring() ? noop : subscribeToObserver();
    return () => queueMicrotask(unsubscribe);
  });
  solidJs.onCleanup(unsubscribe);
  solidJs.onMount(() => {
    observer.setQueries(
      defaultedQueries(),
      queriesOptions().combine ? {
        combine: queriesOptions().combine
      } : void 0
    );
  });
  solidJs.createComputed(() => {
    observer.setQueries(
      defaultedQueries(),
      queriesOptions().combine ? {
        combine: queriesOptions().combine
      } : void 0
    );
  });
  const handler = (index) => ({
    get(target, prop) {
      if (prop === "data") {
        return dataResources()[index][0]();
      }
      return Reflect.get(target, prop);
    }
  });
  const getProxies = () => state.map((s, index) => {
    return new Proxy(s, handler(index));
  });
  const [proxyState, setProxyState] = store.createStore(getProxies());
  solidJs.createRenderEffect(() => setProxyState(getProxies()));
  return proxyState;
}
exports.QueryClient = class QueryClient extends queryCore.QueryClient {
  constructor(config = {}) {
    super(config);
  }
};

// src/queryOptions.ts
function queryOptions(options) {
  return options;
}
function useIsFetching(filters, queryClient) {
  const client = solidJs.createMemo(() => exports.useQueryClient(queryClient?.()));
  const queryCache = solidJs.createMemo(() => client().getQueryCache());
  const [fetches, setFetches] = solidJs.createSignal(client().isFetching(filters?.()));
  const unsubscribe = queryCache().subscribe(() => {
    setFetches(client().isFetching(filters?.()));
  });
  solidJs.onCleanup(unsubscribe);
  return fetches;
}

// src/infiniteQueryOptions.ts
function infiniteQueryOptions(options) {
  return options;
}
function useIsMutating(filters, queryClient) {
  const client = solidJs.createMemo(() => exports.useQueryClient(queryClient?.()));
  const mutationCache = solidJs.createMemo(() => client().getMutationCache());
  const [mutations, setMutations] = solidJs.createSignal(
    client().isMutating(filters?.())
  );
  const unsubscribe = mutationCache().subscribe((_result) => {
    setMutations(client().isMutating(filters?.()));
  });
  solidJs.onCleanup(unsubscribe);
  return mutations;
}
function getResult(mutationCache, options) {
  return mutationCache.findAll(options.filters).map(
    (mutation) => options.select ? options.select(mutation) : mutation.state
  );
}
function useMutationState(options = () => ({}), queryClient) {
  const client = solidJs.createMemo(() => exports.useQueryClient(queryClient?.()));
  const mutationCache = solidJs.createMemo(() => client().getMutationCache());
  const [result, setResult] = solidJs.createSignal(
    getResult(mutationCache(), options())
  );
  solidJs.createEffect(() => {
    const unsubscribe = mutationCache().subscribe(() => {
      const nextResult = queryCore.replaceEqualDeep(
        result(),
        getResult(mutationCache(), options())
      );
      if (result() !== nextResult) {
        setResult(nextResult);
      }
    });
    solidJs.onCleanup(unsubscribe);
  });
  return result;
}

// src/index.ts
exports.createQuery = useQuery;
exports.createInfiniteQuery = useInfiniteQuery;
exports.createMutation = useMutation;
exports.createQueries = useQueries;

exports.infiniteQueryOptions = infiniteQueryOptions;
exports.queryOptions = queryOptions;
exports.useInfiniteQuery = useInfiniteQuery;
exports.useIsFetching = useIsFetching;
exports.useIsMutating = useIsMutating;
exports.useMutation = useMutation;
exports.useMutationState = useMutationState;
exports.useQueries = useQueries;
exports.useQuery = useQuery;
Object.keys(queryCore).forEach(function (k) {
  if (k !== 'default' && !Object.prototype.hasOwnProperty.call(exports, k)) Object.defineProperty(exports, k, {
    enumerable: true,
    get: function () { return queryCore[k]; }
  });
});
