{"name": "@tanstack/solid-query", "version": "5.77.2", "description": "Primitives for managing, caching and syncing asynchronous and remote data in Solid", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/TanStack/query.git", "directory": "packages/solid-query"}, "homepage": "https://tanstack.com/query", "funding": {"type": "github", "url": "https://github.com/sponsors/tanner<PERSON>ley"}, "type": "module", "main": "./build/index.cjs", "module": "./build/index.js", "types": "./build/index.d.ts", "browser": {}, "exports": {"@tanstack/custom-condition": "./src/index.ts", "development": {"import": {"types": "./build/index.d.ts", "default": "./build/dev.js"}, "require": {"types": "./build/index.d.cts", "default": "./build/dev.cjs"}}, "import": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "require": {"types": "./build/index.d.cts", "default": "./build/index.cjs"}}, "files": ["build", "src", "!src/__tests__"], "dependencies": {"@tanstack/query-core": "5.77.2"}, "devDependencies": {"@solidjs/testing-library": "^0.8.10", "npm-run-all2": "^5.0.0", "solid-js": "^1.9.5", "tsup-preset-solid": "^2.2.0", "vite-plugin-solid": "^2.11.6", "@tanstack/query-test-utils": "0.0.0"}, "peerDependencies": {"solid-js": "^1.6.0"}, "scripts": {}}