{"version": 3, "sources": ["../../src/queryClient.ts"], "sourcesContent": ["import {\n  functionalUpdate,\n  hashKey,\n  hashQueryKeyByOptions,\n  noop,\n  partialMatchKey,\n  resolveStaleTime,\n  skipToken,\n} from './utils'\nimport { QueryCache } from './queryCache'\nimport { MutationCache } from './mutationCache'\nimport { focusManager } from './focusManager'\nimport { onlineManager } from './onlineManager'\nimport { notifyManager } from './notifyManager'\nimport { infiniteQueryBehavior } from './infiniteQueryBehavior'\nimport type {\n  CancelOptions,\n  DefaultError,\n  DefaultOptions,\n  DefaultedQueryObserverOptions,\n  EnsureInfiniteQueryDataOptions,\n  EnsureQueryDataOptions,\n  FetchInfiniteQueryOptions,\n  FetchQueryOptions,\n  InferDataFromTag,\n  InferErrorFromTag,\n  InfiniteData,\n  InvalidateOptions,\n  InvalidateQueryFilters,\n  MutationKey,\n  MutationObserverOptions,\n  MutationOptions,\n  NoInfer,\n  OmitKeyof,\n  QueryClientConfig,\n  QueryKey,\n  QueryObserverOptions,\n  QueryOptions,\n  RefetchOptions,\n  RefetchQueryFilters,\n  ResetOptions,\n  SetDataOptions,\n} from './types'\nimport type { QueryState } from './query'\nimport type { MutationFilters, QueryFilters, Updater } from './utils'\n\n// TYPES\n\ninterface QueryDefaults {\n  queryKey: QueryKey\n  defaultOptions: OmitKeyof<QueryOptions<any, any, any>, 'queryKey'>\n}\n\ninterface MutationDefaults {\n  mutationKey: MutationKey\n  defaultOptions: MutationOptions<any, any, any, any>\n}\n\n// CLASS\n\nexport class QueryClient {\n  #queryCache: QueryCache\n  #mutationCache: MutationCache\n  #defaultOptions: DefaultOptions\n  #queryDefaults: Map<string, QueryDefaults>\n  #mutationDefaults: Map<string, MutationDefaults>\n  #mountCount: number\n  #unsubscribeFocus?: () => void\n  #unsubscribeOnline?: () => void\n\n  constructor(config: QueryClientConfig = {}) {\n    this.#queryCache = config.queryCache || new QueryCache()\n    this.#mutationCache = config.mutationCache || new MutationCache()\n    this.#defaultOptions = config.defaultOptions || {}\n    this.#queryDefaults = new Map()\n    this.#mutationDefaults = new Map()\n    this.#mountCount = 0\n  }\n\n  mount(): void {\n    this.#mountCount++\n    if (this.#mountCount !== 1) return\n\n    this.#unsubscribeFocus = focusManager.subscribe(async (focused) => {\n      if (focused) {\n        await this.resumePausedMutations()\n        this.#queryCache.onFocus()\n      }\n    })\n    this.#unsubscribeOnline = onlineManager.subscribe(async (online) => {\n      if (online) {\n        await this.resumePausedMutations()\n        this.#queryCache.onOnline()\n      }\n    })\n  }\n\n  unmount(): void {\n    this.#mountCount--\n    if (this.#mountCount !== 0) return\n\n    this.#unsubscribeFocus?.()\n    this.#unsubscribeFocus = undefined\n\n    this.#unsubscribeOnline?.()\n    this.#unsubscribeOnline = undefined\n  }\n\n  isFetching<TQueryFilters extends QueryFilters<any> = QueryFilters>(\n    filters?: TQueryFilters,\n  ): number {\n    return this.#queryCache.findAll({ ...filters, fetchStatus: 'fetching' })\n      .length\n  }\n\n  isMutating<\n    TMutationFilters extends MutationFilters<any, any> = MutationFilters,\n  >(filters?: TMutationFilters): number {\n    return this.#mutationCache.findAll({ ...filters, status: 'pending' }).length\n  }\n\n  /**\n   * Imperative (non-reactive) way to retrieve data for a QueryKey.\n   * Should only be used in callbacks or functions where reading the latest data is necessary, e.g. for optimistic updates.\n   *\n   * Hint: Do not use this function inside a component, because it won't receive updates.\n   * Use `useQuery` to create a `QueryObserver` that subscribes to changes.\n   */\n  getQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(queryKey: TTaggedQueryKey): TInferredQueryFnData | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n\n    return this.#queryCache.get(options.queryHash)?.state.data as\n      | TInferredQueryFnData\n      | undefined\n  }\n\n  ensureQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: EnsureQueryDataOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n    const query = this.#queryCache.build(this, defaultedOptions)\n    const cachedData = query.state.data\n\n    if (cachedData === undefined) {\n      return this.fetchQuery(options)\n    }\n\n    if (\n      options.revalidateIfStale &&\n      query.isStaleByTime(resolveStaleTime(defaultedOptions.staleTime, query))\n    ) {\n      void this.prefetchQuery(defaultedOptions)\n    }\n\n    return Promise.resolve(cachedData)\n  }\n\n  getQueriesData<\n    TQueryFnData = unknown,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(filters: TQueryFilters): Array<[QueryKey, TQueryFnData | undefined]> {\n    return this.#queryCache.findAll(filters).map(({ queryKey, state }) => {\n      const data = state.data as TQueryFnData | undefined\n      return [queryKey, data]\n    })\n  }\n\n  setQueryData<\n    TQueryFnData = unknown,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n    updater: Updater<\n      NoInfer<TInferredQueryFnData> | undefined,\n      NoInfer<TInferredQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): NoInfer<TInferredQueryFnData> | undefined {\n    const defaultedOptions = this.defaultQueryOptions<\n      any,\n      any,\n      unknown,\n      any,\n      QueryKey\n    >({ queryKey })\n\n    const query = this.#queryCache.get<TInferredQueryFnData>(\n      defaultedOptions.queryHash,\n    )\n    const prevData = query?.state.data\n    const data = functionalUpdate(updater, prevData)\n\n    if (data === undefined) {\n      return undefined\n    }\n\n    return this.#queryCache\n      .build(this, defaultedOptions)\n      .setData(data, { ...options, manual: true })\n  }\n\n  setQueriesData<\n    TQueryFnData,\n    TQueryFilters extends QueryFilters<any> = QueryFilters,\n  >(\n    filters: TQueryFilters,\n    updater: Updater<\n      NoInfer<TQueryFnData> | undefined,\n      NoInfer<TQueryFnData> | undefined\n    >,\n    options?: SetDataOptions,\n  ): Array<[QueryKey, TQueryFnData | undefined]> {\n    return notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map(({ queryKey }) => [\n          queryKey,\n          this.setQueryData<TQueryFnData>(queryKey, updater, options),\n        ]),\n    )\n  }\n\n  getQueryState<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TTaggedQueryKey extends QueryKey = QueryKey,\n    TInferredQueryFnData = InferDataFromTag<TQueryFnData, TTaggedQueryKey>,\n    TInferredError = InferErrorFromTag<TError, TTaggedQueryKey>,\n  >(\n    queryKey: TTaggedQueryKey,\n  ): QueryState<TInferredQueryFnData, TInferredError> | undefined {\n    const options = this.defaultQueryOptions({ queryKey })\n    return this.#queryCache.get<TInferredQueryFnData, TInferredError>(\n      options.queryHash,\n    )?.state\n  }\n\n  removeQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n  ): void {\n    const queryCache = this.#queryCache\n    notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        queryCache.remove(query)\n      })\n    })\n  }\n\n  resetQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    options?: ResetOptions,\n  ): Promise<void> {\n    const queryCache = this.#queryCache\n\n    return notifyManager.batch(() => {\n      queryCache.findAll(filters).forEach((query) => {\n        query.reset()\n      })\n      return this.refetchQueries(\n        {\n          type: 'active',\n          ...filters,\n        },\n        options,\n      )\n    })\n  }\n\n  cancelQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: QueryFilters<TTaggedQueryKey>,\n    cancelOptions: CancelOptions = {},\n  ): Promise<void> {\n    const defaultedCancelOptions = { revert: true, ...cancelOptions }\n\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .map((query) => query.cancel(defaultedCancelOptions)),\n    )\n\n    return Promise.all(promises).then(noop).catch(noop)\n  }\n\n  invalidateQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: InvalidateQueryFilters<TTaggedQueryKey>,\n    options: InvalidateOptions = {},\n  ): Promise<void> {\n    return notifyManager.batch(() => {\n      this.#queryCache.findAll(filters).forEach((query) => {\n        query.invalidate()\n      })\n\n      if (filters?.refetchType === 'none') {\n        return Promise.resolve()\n      }\n      return this.refetchQueries(\n        {\n          ...filters,\n          type: filters?.refetchType ?? filters?.type ?? 'active',\n        },\n        options,\n      )\n    })\n  }\n\n  refetchQueries<TTaggedQueryKey extends QueryKey = QueryKey>(\n    filters?: RefetchQueryFilters<TTaggedQueryKey>,\n    options: RefetchOptions = {},\n  ): Promise<void> {\n    const fetchOptions = {\n      ...options,\n      cancelRefetch: options.cancelRefetch ?? true,\n    }\n    const promises = notifyManager.batch(() =>\n      this.#queryCache\n        .findAll(filters)\n        .filter((query) => !query.isDisabled())\n        .map((query) => {\n          let promise = query.fetch(undefined, fetchOptions)\n          if (!fetchOptions.throwOnError) {\n            promise = promise.catch(noop)\n          }\n          return query.state.fetchStatus === 'paused'\n            ? Promise.resolve()\n            : promise\n        }),\n    )\n\n    return Promise.all(promises).then(noop)\n  }\n\n  fetchQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options: FetchQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<TData> {\n    const defaultedOptions = this.defaultQueryOptions(options)\n\n    // https://github.com/tannerlinsley/react-query/issues/652\n    if (defaultedOptions.retry === undefined) {\n      defaultedOptions.retry = false\n    }\n\n    const query = this.#queryCache.build(this, defaultedOptions)\n\n    return query.isStaleByTime(\n      resolveStaleTime(defaultedOptions.staleTime, query),\n    )\n      ? query.fetch(defaultedOptions)\n      : Promise.resolve(query.state.data as TData)\n  }\n\n  prefetchQuery<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n  >(\n    options: FetchQueryOptions<TQueryFnData, TError, TData, TQueryKey>,\n  ): Promise<void> {\n    return this.fetchQuery(options).then(noop).catch(noop)\n  }\n\n  fetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n    return this.fetchQuery(options as any)\n  }\n\n  prefetchInfiniteQuery<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: FetchInfiniteQueryOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<void> {\n    return this.fetchInfiniteQuery(options).then(noop).catch(noop)\n  }\n\n  ensureInfiniteQueryData<\n    TQueryFnData,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = unknown,\n  >(\n    options: EnsureInfiniteQueryDataOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryKey,\n      TPageParam\n    >,\n  ): Promise<InfiniteData<TData, TPageParam>> {\n    options.behavior = infiniteQueryBehavior<\n      TQueryFnData,\n      TError,\n      TData,\n      TPageParam\n    >(options.pages)\n\n    return this.ensureQueryData(options as any)\n  }\n\n  resumePausedMutations(): Promise<unknown> {\n    if (onlineManager.isOnline()) {\n      return this.#mutationCache.resumePausedMutations()\n    }\n    return Promise.resolve()\n  }\n\n  getQueryCache(): QueryCache {\n    return this.#queryCache\n  }\n\n  getMutationCache(): MutationCache {\n    return this.#mutationCache\n  }\n\n  getDefaultOptions(): DefaultOptions {\n    return this.#defaultOptions\n  }\n\n  setDefaultOptions(options: DefaultOptions): void {\n    this.#defaultOptions = options\n  }\n\n  setQueryDefaults<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n  >(\n    queryKey: QueryKey,\n    options: Partial<\n      OmitKeyof<\n        QueryObserverOptions<TQueryFnData, TError, TData, TQueryData>,\n        'queryKey'\n      >\n    >,\n  ): void {\n    this.#queryDefaults.set(hashKey(queryKey), {\n      queryKey,\n      defaultOptions: options,\n    })\n  }\n\n  getQueryDefaults(\n    queryKey: QueryKey,\n  ): OmitKeyof<QueryObserverOptions<any, any, any, any, any>, 'queryKey'> {\n    const defaults = [...this.#queryDefaults.values()]\n\n    const result: OmitKeyof<\n      QueryObserverOptions<any, any, any, any, any>,\n      'queryKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(queryKey, queryDefault.queryKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n    return result\n  }\n\n  setMutationDefaults<\n    TData = unknown,\n    TError = DefaultError,\n    TVariables = void,\n    TContext = unknown,\n  >(\n    mutationKey: MutationKey,\n    options: OmitKeyof<\n      MutationObserverOptions<TData, TError, TVariables, TContext>,\n      'mutationKey'\n    >,\n  ): void {\n    this.#mutationDefaults.set(hashKey(mutationKey), {\n      mutationKey,\n      defaultOptions: options,\n    })\n  }\n\n  getMutationDefaults(\n    mutationKey: MutationKey,\n  ): OmitKeyof<MutationObserverOptions<any, any, any, any>, 'mutationKey'> {\n    const defaults = [...this.#mutationDefaults.values()]\n\n    const result: OmitKeyof<\n      MutationObserverOptions<any, any, any, any>,\n      'mutationKey'\n    > = {}\n\n    defaults.forEach((queryDefault) => {\n      if (partialMatchKey(mutationKey, queryDefault.mutationKey)) {\n        Object.assign(result, queryDefault.defaultOptions)\n      }\n    })\n\n    return result\n  }\n\n  defaultQueryOptions<\n    TQueryFnData = unknown,\n    TError = DefaultError,\n    TData = TQueryFnData,\n    TQueryData = TQueryFnData,\n    TQueryKey extends QueryKey = QueryKey,\n    TPageParam = never,\n  >(\n    options:\n      | QueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey,\n          TPageParam\n        >\n      | DefaultedQueryObserverOptions<\n          TQueryFnData,\n          TError,\n          TData,\n          TQueryData,\n          TQueryKey\n        >,\n  ): DefaultedQueryObserverOptions<\n    TQueryFnData,\n    TError,\n    TData,\n    TQueryData,\n    TQueryKey\n  > {\n    if (options._defaulted) {\n      return options as DefaultedQueryObserverOptions<\n        TQueryFnData,\n        TError,\n        TData,\n        TQueryData,\n        TQueryKey\n      >\n    }\n\n    const defaultedOptions = {\n      ...this.#defaultOptions.queries,\n      ...this.getQueryDefaults(options.queryKey),\n      ...options,\n      _defaulted: true,\n    }\n\n    if (!defaultedOptions.queryHash) {\n      defaultedOptions.queryHash = hashQueryKeyByOptions(\n        defaultedOptions.queryKey,\n        defaultedOptions,\n      )\n    }\n\n    // dependent default values\n    if (defaultedOptions.refetchOnReconnect === undefined) {\n      defaultedOptions.refetchOnReconnect =\n        defaultedOptions.networkMode !== 'always'\n    }\n    if (defaultedOptions.throwOnError === undefined) {\n      defaultedOptions.throwOnError = !!defaultedOptions.suspense\n    }\n\n    if (!defaultedOptions.networkMode && defaultedOptions.persister) {\n      defaultedOptions.networkMode = 'offlineFirst'\n    }\n\n    if (defaultedOptions.queryFn === skipToken) {\n      defaultedOptions.enabled = false\n    }\n\n    return defaultedOptions as DefaultedQueryObserverOptions<\n      TQueryFnData,\n      TError,\n      TData,\n      TQueryData,\n      TQueryKey\n    >\n  }\n\n  defaultMutationOptions<T extends MutationOptions<any, any, any, any>>(\n    options?: T,\n  ): T {\n    if (options?._defaulted) {\n      return options\n    }\n    return {\n      ...this.#defaultOptions.mutations,\n      ...(options?.mutationKey &&\n        this.getMutationDefaults(options.mutationKey)),\n      ...options,\n      _defaulted: true,\n    } as T\n  }\n\n  clear(): void {\n    this.#queryCache.clear()\n    this.#mutationCache.clear()\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAQO;AACP,wBAA2B;AAC3B,2BAA8B;AAC9B,0BAA6B;AAC7B,2BAA8B;AAC9B,2BAA8B;AAC9B,mCAAsC;AAdtC;AA4DO,IAAM,cAAN,MAAkB;AAAA,EAUvB,YAAY,SAA4B,CAAC,GAAG;AAT5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGE,uBAAK,aAAc,OAAO,cAAc,IAAI,6BAAW;AACvD,uBAAK,gBAAiB,OAAO,iBAAiB,IAAI,mCAAc;AAChE,uBAAK,iBAAkB,OAAO,kBAAkB,CAAC;AACjD,uBAAK,gBAAiB,oBAAI,IAAI;AAC9B,uBAAK,mBAAoB,oBAAI,IAAI;AACjC,uBAAK,aAAc;AAAA,EACrB;AAAA,EAEA,QAAc;AACZ,2BAAK,aAAL;AACA,QAAI,mBAAK,iBAAgB,EAAG;AAE5B,uBAAK,mBAAoB,iCAAa,UAAU,OAAO,YAAY;AACjE,UAAI,SAAS;AACX,cAAM,KAAK,sBAAsB;AACjC,2BAAK,aAAY,QAAQ;AAAA,MAC3B;AAAA,IACF,CAAC;AACD,uBAAK,oBAAqB,mCAAc,UAAU,OAAO,WAAW;AAClE,UAAI,QAAQ;AACV,cAAM,KAAK,sBAAsB;AACjC,2BAAK,aAAY,SAAS;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,UAAgB;AAjGlB;AAkGI,2BAAK,aAAL;AACA,QAAI,mBAAK,iBAAgB,EAAG;AAE5B,6BAAK,uBAAL;AACA,uBAAK,mBAAoB;AAEzB,6BAAK,wBAAL;AACA,uBAAK,oBAAqB;AAAA,EAC5B;AAAA,EAEA,WACE,SACQ;AACR,WAAO,mBAAK,aAAY,QAAQ,EAAE,GAAG,SAAS,aAAa,WAAW,CAAC,EACpE;AAAA,EACL;AAAA,EAEA,WAEE,SAAoC;AACpC,WAAO,mBAAK,gBAAe,QAAQ,EAAE,GAAG,SAAS,QAAQ,UAAU,CAAC,EAAE;AAAA,EACxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,aAIE,UAA6D;AApIjE;AAqII,UAAM,UAAU,KAAK,oBAAoB,EAAE,SAAS,CAAC;AAErD,YAAO,wBAAK,aAAY,IAAI,QAAQ,SAAS,MAAtC,mBAAyC,MAAM;AAAA,EAGxD;AAAA,EAEA,gBAME,SACgB;AAChB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AACzD,UAAM,QAAQ,mBAAK,aAAY,MAAM,MAAM,gBAAgB;AAC3D,UAAM,aAAa,MAAM,MAAM;AAE/B,QAAI,eAAe,QAAW;AAC5B,aAAO,KAAK,WAAW,OAAO;AAAA,IAChC;AAEA,QACE,QAAQ,qBACR,MAAM,kBAAc,+BAAiB,iBAAiB,WAAW,KAAK,CAAC,GACvE;AACA,WAAK,KAAK,cAAc,gBAAgB;AAAA,IAC1C;AAEA,WAAO,QAAQ,QAAQ,UAAU;AAAA,EACnC;AAAA,EAEA,eAGE,SAAqE;AACrE,WAAO,mBAAK,aAAY,QAAQ,OAAO,EAAE,IAAI,CAAC,EAAE,UAAU,MAAM,MAAM;AACpE,YAAM,OAAO,MAAM;AACnB,aAAO,CAAC,UAAU,IAAI;AAAA,IACxB,CAAC;AAAA,EACH;AAAA,EAEA,aAKE,UACA,SAIA,SAC2C;AAC3C,UAAM,mBAAmB,KAAK,oBAM5B,EAAE,SAAS,CAAC;AAEd,UAAM,QAAQ,mBAAK,aAAY;AAAA,MAC7B,iBAAiB;AAAA,IACnB;AACA,UAAM,WAAW,+BAAO,MAAM;AAC9B,UAAM,WAAO,+BAAiB,SAAS,QAAQ;AAE/C,QAAI,SAAS,QAAW;AACtB,aAAO;AAAA,IACT;AAEA,WAAO,mBAAK,aACT,MAAM,MAAM,gBAAgB,EAC5B,QAAQ,MAAM,EAAE,GAAG,SAAS,QAAQ,KAAK,CAAC;AAAA,EAC/C;AAAA,EAEA,eAIE,SACA,SAIA,SAC6C;AAC7C,WAAO,mCAAc;AAAA,MAAM,MACzB,mBAAK,aACF,QAAQ,OAAO,EACf,IAAI,CAAC,EAAE,SAAS,MAAM;AAAA,QACrB;AAAA,QACA,KAAK,aAA2B,UAAU,SAAS,OAAO;AAAA,MAC5D,CAAC;AAAA,IACL;AAAA,EACF;AAAA,EAEA,cAOE,UAC8D;AAhPlE;AAiPI,UAAM,UAAU,KAAK,oBAAoB,EAAE,SAAS,CAAC;AACrD,YAAO,wBAAK,aAAY;AAAA,MACtB,QAAQ;AAAA,IACV,MAFO,mBAEJ;AAAA,EACL;AAAA,EAEA,cACE,SACM;AACN,UAAM,aAAa,mBAAK;AACxB,uCAAc,MAAM,MAAM;AACxB,iBAAW,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC7C,mBAAW,OAAO,KAAK;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAAA,EAEA,aACE,SACA,SACe;AACf,UAAM,aAAa,mBAAK;AAExB,WAAO,mCAAc,MAAM,MAAM;AAC/B,iBAAW,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AAC7C,cAAM,MAAM;AAAA,MACd,CAAC;AACD,aAAO,KAAK;AAAA,QACV;AAAA,UACE,MAAM;AAAA,UACN,GAAG;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,cACE,SACA,gBAA+B,CAAC,GACjB;AACf,UAAM,yBAAyB,EAAE,QAAQ,MAAM,GAAG,cAAc;AAEhE,UAAM,WAAW,mCAAc;AAAA,MAAM,MACnC,mBAAK,aACF,QAAQ,OAAO,EACf,IAAI,CAAC,UAAU,MAAM,OAAO,sBAAsB,CAAC;AAAA,IACxD;AAEA,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,iBAAI,EAAE,MAAM,iBAAI;AAAA,EACpD;AAAA,EAEA,kBACE,SACA,UAA6B,CAAC,GACf;AACf,WAAO,mCAAc,MAAM,MAAM;AAC/B,yBAAK,aAAY,QAAQ,OAAO,EAAE,QAAQ,CAAC,UAAU;AACnD,cAAM,WAAW;AAAA,MACnB,CAAC;AAED,WAAI,mCAAS,iBAAgB,QAAQ;AACnC,eAAO,QAAQ,QAAQ;AAAA,MACzB;AACA,aAAO,KAAK;AAAA,QACV;AAAA,UACE,GAAG;AAAA,UACH,OAAM,mCAAS,iBAAe,mCAAS,SAAQ;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEA,eACE,SACA,UAA0B,CAAC,GACZ;AACf,UAAM,eAAe;AAAA,MACnB,GAAG;AAAA,MACH,eAAe,QAAQ,iBAAiB;AAAA,IAC1C;AACA,UAAM,WAAW,mCAAc;AAAA,MAAM,MACnC,mBAAK,aACF,QAAQ,OAAO,EACf,OAAO,CAAC,UAAU,CAAC,MAAM,WAAW,CAAC,EACrC,IAAI,CAAC,UAAU;AACd,YAAI,UAAU,MAAM,MAAM,QAAW,YAAY;AACjD,YAAI,CAAC,aAAa,cAAc;AAC9B,oBAAU,QAAQ,MAAM,iBAAI;AAAA,QAC9B;AACA,eAAO,MAAM,MAAM,gBAAgB,WAC/B,QAAQ,QAAQ,IAChB;AAAA,MACN,CAAC;AAAA,IACL;AAEA,WAAO,QAAQ,IAAI,QAAQ,EAAE,KAAK,iBAAI;AAAA,EACxC;AAAA,EAEA,WAOE,SAOgB;AAChB,UAAM,mBAAmB,KAAK,oBAAoB,OAAO;AAGzD,QAAI,iBAAiB,UAAU,QAAW;AACxC,uBAAiB,QAAQ;AAAA,IAC3B;AAEA,UAAM,QAAQ,mBAAK,aAAY,MAAM,MAAM,gBAAgB;AAE3D,WAAO,MAAM;AAAA,UACX,+BAAiB,iBAAiB,WAAW,KAAK;AAAA,IACpD,IACI,MAAM,MAAM,gBAAgB,IAC5B,QAAQ,QAAQ,MAAM,MAAM,IAAa;AAAA,EAC/C;AAAA,EAEA,cAME,SACe;AACf,WAAO,KAAK,WAAW,OAAO,EAAE,KAAK,iBAAI,EAAE,MAAM,iBAAI;AAAA,EACvD;AAAA,EAEA,mBAOE,SAO0C;AAC1C,YAAQ,eAAW,oDAKjB,QAAQ,KAAK;AACf,WAAO,KAAK,WAAW,OAAc;AAAA,EACvC;AAAA,EAEA,sBAOE,SAOe;AACf,WAAO,KAAK,mBAAmB,OAAO,EAAE,KAAK,iBAAI,EAAE,MAAM,iBAAI;AAAA,EAC/D;AAAA,EAEA,wBAOE,SAO0C;AAC1C,YAAQ,eAAW,oDAKjB,QAAQ,KAAK;AAEf,WAAO,KAAK,gBAAgB,OAAc;AAAA,EAC5C;AAAA,EAEA,wBAA0C;AACxC,QAAI,mCAAc,SAAS,GAAG;AAC5B,aAAO,mBAAK,gBAAe,sBAAsB;AAAA,IACnD;AACA,WAAO,QAAQ,QAAQ;AAAA,EACzB;AAAA,EAEA,gBAA4B;AAC1B,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,mBAAkC;AAChC,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,oBAAoC;AAClC,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,kBAAkB,SAA+B;AAC/C,uBAAK,iBAAkB;AAAA,EACzB;AAAA,EAEA,iBAME,UACA,SAMM;AACN,uBAAK,gBAAe,QAAI,sBAAQ,QAAQ,GAAG;AAAA,MACzC;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EAEA,iBACE,UACsE;AACtE,UAAM,WAAW,CAAC,GAAG,mBAAK,gBAAe,OAAO,CAAC;AAEjD,UAAM,SAGF,CAAC;AAEL,aAAS,QAAQ,CAAC,iBAAiB;AACjC,cAAI,8BAAgB,UAAU,aAAa,QAAQ,GAAG;AACpD,eAAO,OAAO,QAAQ,aAAa,cAAc;AAAA,MACnD;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EAEA,oBAME,aACA,SAIM;AACN,uBAAK,mBAAkB,QAAI,sBAAQ,WAAW,GAAG;AAAA,MAC/C;AAAA,MACA,gBAAgB;AAAA,IAClB,CAAC;AAAA,EACH;AAAA,EAEA,oBACE,aACuE;AACvE,UAAM,WAAW,CAAC,GAAG,mBAAK,mBAAkB,OAAO,CAAC;AAEpD,UAAM,SAGF,CAAC;AAEL,aAAS,QAAQ,CAAC,iBAAiB;AACjC,cAAI,8BAAgB,aAAa,aAAa,WAAW,GAAG;AAC1D,eAAO,OAAO,QAAQ,aAAa,cAAc;AAAA,MACnD;AAAA,IACF,CAAC;AAED,WAAO;AAAA,EACT;AAAA,EAEA,oBAQE,SAsBA;AACA,QAAI,QAAQ,YAAY;AACtB,aAAO;AAAA,IAOT;AAEA,UAAM,mBAAmB;AAAA,MACvB,GAAG,mBAAK,iBAAgB;AAAA,MACxB,GAAG,KAAK,iBAAiB,QAAQ,QAAQ;AAAA,MACzC,GAAG;AAAA,MACH,YAAY;AAAA,IACd;AAEA,QAAI,CAAC,iBAAiB,WAAW;AAC/B,uBAAiB,gBAAY;AAAA,QAC3B,iBAAiB;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AAGA,QAAI,iBAAiB,uBAAuB,QAAW;AACrD,uBAAiB,qBACf,iBAAiB,gBAAgB;AAAA,IACrC;AACA,QAAI,iBAAiB,iBAAiB,QAAW;AAC/C,uBAAiB,eAAe,CAAC,CAAC,iBAAiB;AAAA,IACrD;AAEA,QAAI,CAAC,iBAAiB,eAAe,iBAAiB,WAAW;AAC/D,uBAAiB,cAAc;AAAA,IACjC;AAEA,QAAI,iBAAiB,YAAY,wBAAW;AAC1C,uBAAiB,UAAU;AAAA,IAC7B;AAEA,WAAO;AAAA,EAOT;AAAA,EAEA,uBACE,SACG;AACH,QAAI,mCAAS,YAAY;AACvB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,MACL,GAAG,mBAAK,iBAAgB;AAAA,MACxB,IAAI,mCAAS,gBACX,KAAK,oBAAoB,QAAQ,WAAW;AAAA,MAC9C,GAAG;AAAA,MACH,YAAY;AAAA,IACd;AAAA,EACF;AAAA,EAEA,QAAc;AACZ,uBAAK,aAAY,MAAM;AACvB,uBAAK,gBAAe,MAAM;AAAA,EAC5B;AACF;AA3kBE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;", "names": []}