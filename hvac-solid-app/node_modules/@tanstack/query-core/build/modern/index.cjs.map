{"version": 3, "sources": ["../../src/index.ts"], "sourcesContent": ["/* istanbul ignore file */\n\nexport { CancelledError } from './retryer'\nexport { QueryCache } from './queryCache'\nexport type { QueryCacheNotifyEvent } from './queryCache'\nexport { QueryClient } from './queryClient'\nexport { QueryObserver } from './queryObserver'\nexport { QueriesObserver } from './queriesObserver'\nexport { InfiniteQueryObserver } from './infiniteQueryObserver'\nexport { MutationCache } from './mutationCache'\nexport type { MutationCacheNotifyEvent } from './mutationCache'\nexport { MutationObserver } from './mutationObserver'\nexport { notifyManager, defaultScheduler } from './notifyManager'\nexport { focusManager } from './focusManager'\nexport { onlineManager } from './onlineManager'\nexport {\n  hashKey,\n  replaceEqualDeep,\n  isServer,\n  matchQuery,\n  matchMutation,\n  keepPreviousData,\n  skipToken,\n  noop,\n  shouldThrowError,\n} from './utils'\nexport type { MutationFilters, QueryFilters, Updater, SkipToken } from './utils'\nexport { isCancelledError } from './retryer'\nexport {\n  dehydrate,\n  hydrate,\n  defaultShouldDehydrateQuery,\n  defaultShouldDehydrateMutation,\n} from './hydration'\n\nexport { streamedQuery as experimental_streamedQuery } from './streamedQuery'\n\n// Types\nexport * from './types'\nexport type { QueryState } from './query'\nexport { Query } from './query'\nexport type { MutationState } from './mutation'\nexport { Mutation } from './mutation'\nexport type {\n  DehydrateOptions,\n  DehydratedState,\n  HydrateOptions,\n} from './hydration'\nexport type { QueriesObserverOptions } from './queriesObserver'\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA,qBAA+B;AAC/B,wBAA2B;AAE3B,yBAA4B;AAC5B,2BAA8B;AAC9B,6BAAgC;AAChC,mCAAsC;AACtC,2BAA8B;AAE9B,8BAAiC;AACjC,2BAAgD;AAChD,0BAA6B;AAC7B,2BAA8B;AAC9B,mBAUO;AAEP,IAAAA,kBAAiC;AACjC,uBAKO;AAEP,2BAA4D;AAG5D,0BAAc,wBAtCd;AAwCA,mBAAsB;AAEtB,sBAAyB;", "names": ["import_retryer"]}