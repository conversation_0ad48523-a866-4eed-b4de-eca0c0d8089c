{"name": "@solidjs/router", "description": "Universal router for SolidJS", "author": "<PERSON>", "contributors": ["<PERSON>"], "license": "MIT", "version": "0.15.3", "homepage": "https://github.com/solidjs/solid-router#readme", "repository": {"type": "git", "url": "https://github.com/solidjs/solid-router"}, "publishConfig": {"access": "public"}, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"solid": "./dist/index.jsx", "default": "./dist/index.js"}}, "files": ["dist"], "sideEffects": false, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-typescript": "^7.26.0", "@changesets/cli": "^2.27.10", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-node-resolve": "15.3.0", "@rollup/plugin-terser": "0.4.4", "@types/jest": "^29.5.14", "@types/node": "^22.10.0", "babel-preset-solid": "^1.9.3", "jsdom": "^25.0.1", "prettier": "^3.4.1", "rollup": "^4.27.4", "solid-js": "^1.9.3", "typescript": "^5.7.2", "vite": "^6.0.0", "vite-plugin-solid": "^2.11.0", "vitest": "^2.1.6"}, "peerDependencies": {"solid-js": "^1.8.6"}, "scripts": {"build": "tsc && rollup -c", "test": "vitest run && npm run test:types", "test:watch": "vitest", "test:types": "tsc --project tsconfig.test.json", "pretty": "prettier --write \"{src,test}/**/*.{ts,tsx}\""}}