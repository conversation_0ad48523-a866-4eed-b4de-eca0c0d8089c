{"name": "@solidjs/meta", "description": "Write meta tags to the document head", "version": "0.29.4", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/solidjs/solid-meta"}, "publishConfig": {"access": "public"}, "type": "module", "main": "dist/index.js", "exports": {".": {"solid": "./dist/index.jsx", "default": "./dist/index.js"}}, "types": "dist/index.d.ts", "files": ["dist"], "sideEffects": false, "peerDependencies": {"solid-js": ">=1.8.4"}, "devDependencies": {"@babel/cli": "^7.14.3", "@babel/core": "7.14.3", "@babel/preset-env": "7.14.4", "@babel/preset-typescript": "7.13.0", "@types/jest": "^26.0.23", "babel-preset-solid": "^1.8.4", "jest": "^26.6.3", "rimraf": "^3.0.2", "solid-jest": "0.1.1", "solid-js": "^1.8.4", "typescript": "4.9.4"}, "jest": {"preset": "solid-jest/preset/browser"}, "scripts": {"prebuild": "npm run clean", "clean": "rimraf dist/", "build": "tsc && babel src/index.tsx --out-file dist/index.js", "test": "jest && npm run test:types", "test:types": "tsc --project tsconfig.test.json"}}