import type { Communication as PrismaCommunication } from "@prisma/client";
import { prisma } from "~/db.server";
import { sendEmail } from "~/utils/email.server";
import { sendSMS } from "~/utils/sms.server";

export interface Communication extends PrismaCommunication {}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
  success: boolean;
}

export interface PaginationParams {
  page?: number;
  pageSize?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
}

export interface FilterParams {
  channel?: string;
  direction?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface PaginatedResponse<T> extends ServiceResponse<T[]> {
  totalCount: number;
  totalPages: number;
  currentPage: number;
}

/**
 * Get all communications for a customer
 */
export async function getCustomerCommunications(
  customerId: string,
  userId: string,
  pagination: PaginationParams = {},
  filters: FilterParams = {}
): Promise<ServiceResponse<Communication[]>> {
  try {
    // Verify customer exists and belongs to user
    const customer = await prisma.customer.findUnique({
      where: { id: customerId, userId },
    });

    if (!customer) {
      return {
        data: null,
        error: 'Customer not found or access denied',
        success: false
      };
    }

    // Build where clause
    const where: any = { customerId };

    // Add channel filter if provided
    if (filters.channel) {
      where.channel = filters.channel;
    }

    // Add direction filter if provided
    if (filters.direction) {
      where.direction = filters.direction;
    }

    // Add date range filter if provided
    if (filters.startDate || filters.endDate) {
      where.timestamp = {};
      if (filters.startDate) {
        where.timestamp.gte = filters.startDate;
      }
      if (filters.endDate) {
        where.timestamp.lte = filters.endDate;
      }
    }

    // Get communications
    const communications = await prisma.communication.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      include: {
        attachments: true,
      },
    });

    return {
      data: communications,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch communications:', error);
    return {
      data: null,
      error: 'Failed to fetch communications',
      success: false
    };
  }
}

/**
 * Send a communication to a customer
 */
export async function sendCustomerCommunication(
  data: {
    customerId: string;
    userId: string;
    channel: string;
    subject?: string;
    content: string;
    direction: 'OUTBOUND';
    attachments?: Array<{
      name: string;
      url: string;
      type: string;
    }>;
  }
): Promise<ServiceResponse<Communication>> {
  try {
    // Verify customer exists and belongs to user
    const customer = await prisma.customer.findUnique({
      where: { id: data.customerId, userId: data.userId },
      select: {
        id: true,
        name: true,
        email: true,
        phone: true,
      },
    });

    if (!customer) {
      return {
        data: null,
        error: 'Customer not found or access denied',
        success: false
      };
    }

    // Create communication record
    const communication = await prisma.communication.create({
      data: {
        customerId: data.customerId,
        userId: data.userId,
        channel: data.channel,
        subject: data.subject,
        content: data.content,
        direction: data.direction,
        timestamp: new Date(),
        read: false,
        attachments: data.attachments ? {
          createMany: {
            data: data.attachments,
          },
        } : undefined,
      },
      include: {
        attachments: true,
      },
    });

    // Actually send the communication based on channel
    if (data.channel === 'EMAIL' && customer.email) {
      await sendEmail({
        to: customer.email,
        subject: data.subject || 'Message from HVAC CRM',
        text: data.content,
        // Add attachments if any
        attachments: data.attachments?.map(attachment => ({
          filename: attachment.name,
          path: attachment.url,
        })),
      });
    } else if (data.channel === 'SMS' && customer.phone) {
      await sendSMS({
        to: customer.phone,
        message: data.content,
      });
    }

    return {
      data: communication,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to send communication:', error);
    return {
      data: null,
      error: 'Failed to send communication',
      success: false
    };
  }
}

/**
 * Mark a communication as read
 */
export async function markCommunicationAsRead(
  id: string,
  userId: string
): Promise<ServiceResponse<Communication>> {
  try {
    // Verify communication exists and belongs to user
    const communication = await prisma.communication.findFirst({
      where: {
        id,
        OR: [
          { userId },
          { customer: { userId } },
        ],
      },
    });

    if (!communication) {
      return {
        data: null,
        error: 'Communication not found or access denied',
        success: false
      };
    }

    // Update communication
    const updatedCommunication = await prisma.communication.update({
      where: { id },
      data: { read: true },
    });

    return {
      data: updatedCommunication,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to mark communication as read:', error);
    return {
      data: null,
      error: 'Failed to mark communication as read',
      success: false
    };
  }
}

/**
 * Get communication templates
 */
export async function getCommunicationTemplates(
  userId: string,
  type: string
): Promise<ServiceResponse<any[]>> {
  try {
    const templates = await prisma.communicationTemplate.findMany({
      where: {
        userId,
        type,
      },
    });

    return {
      data: templates,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to fetch communication templates:', error);
    return {
      data: null,
      error: 'Failed to fetch communication templates',
      success: false
    };
  }
}

/**
 * Create a communication template
 */
export async function createCommunicationTemplate(
  data: {
    userId: string;
    name: string;
    type: string;
    subject?: string;
    content: string;
  }
): Promise<ServiceResponse<any>> {
  try {
    const template = await prisma.communicationTemplate.create({
      data,
    });

    return {
      data: template,
      error: null,
      success: true
    };
  } catch (error) {
    console.error('Failed to create communication template:', error);
    return {
      data: null,
      error: 'Failed to create communication template',
      success: false
    };
  }
}
