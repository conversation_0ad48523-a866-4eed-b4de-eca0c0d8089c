(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))i(r);new MutationObserver(r=>{for(const o of r)if(o.type==="childList")for(const l of o.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&i(l)}).observe(document,{childList:!0,subtree:!0});function n(r){const o={};return r.integrity&&(o.integrity=r.integrity),r.referrerPolicy&&(o.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?o.credentials="include":r.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(r){if(r.ep)return;r.ep=!0;const o=n(r);fetch(r.href,o)}})();const ue=!1,fe=(e,t)=>e===t,L=Symbol("solid-proxy"),ee=typeof Proxy=="function",j={equals:fe};let te=re;const x=1,k=2,ne={owned:null,cleanups:null,context:null,owner:null};var p=null;let G=null,de=null,y=null,b=null,w=null,B=0;function he(e,t){const n=y,i=p,r=e.length===0,o=t===void 0?i:t,l=r?ne:{owned:null,cleanups:null,context:o?o.context:null,owner:o},s=r?e:()=>e(()=>A(()=>S(l)));p=l,y=null;try{return E(s,!0)}finally{y=n,p=i}}function X(e,t){t=t?Object.assign({},j,t):j;const n={value:e,observers:null,observerSlots:null,comparator:t.equals||void 0},i=r=>(typeof r=="function"&&(r=r(n.value)),ie(n,r));return[se.bind(n),i]}function $(e,t,n){const i=H(e,t,!1,x);O(i)}function ge(e,t,n){te=ve;const i=H(e,t,!1,x);i.user=!0,w?w.push(i):O(i)}function ye(e,t,n){n=n?Object.assign({},j,n):j;const i=H(e,t,!0,0);return i.observers=null,i.observerSlots=null,i.comparator=n.equals||void 0,O(i),se.bind(i)}function A(e){if(y===null)return e();const t=y;y=null;try{return e()}finally{y=t}}function me(e){ge(()=>A(e))}function se(){if(this.sources&&this.state)if(this.state===x)O(this);else{const e=b;b=null,E(()=>I(this),!1),b=e}if(y){const e=this.observers?this.observers.length:0;y.sources?(y.sources.push(this),y.sourceSlots.push(e)):(y.sources=[this],y.sourceSlots=[e]),this.observers?(this.observers.push(y),this.observerSlots.push(y.sources.length-1)):(this.observers=[y],this.observerSlots=[y.sources.length-1])}return this.value}function ie(e,t,n){let i=e.value;return(!e.comparator||!e.comparator(i,t))&&(e.value=t,e.observers&&e.observers.length&&E(()=>{for(let r=0;r<e.observers.length;r+=1){const o=e.observers[r],l=G&&G.running;l&&G.disposed.has(o),(l?!o.tState:!o.state)&&(o.pure?b.push(o):w.push(o),o.observers&&oe(o)),l||(o.state=x)}if(b.length>1e6)throw b=[],new Error},!1)),t}function O(e){if(!e.fn)return;S(e);const t=B;be(e,e.value,t)}function be(e,t,n){let i;const r=p,o=y;y=p=e;try{i=e.fn(t)}catch(l){return e.pure&&(e.state=x,e.owned&&e.owned.forEach(S),e.owned=null),e.updatedAt=n+1,le(l)}finally{y=o,p=r}(!e.updatedAt||e.updatedAt<=n)&&(e.updatedAt!=null&&"observers"in e?ie(e,i):e.value=i,e.updatedAt=n)}function H(e,t,n,i=x,r){const o={fn:e,state:i,updatedAt:null,owned:null,sources:null,sourceSlots:null,cleanups:null,value:t,owner:p,context:p?p.context:null,pure:n};return p===null||p!==ne&&(p.owned?p.owned.push(o):p.owned=[o]),o}function M(e){if(e.state===0)return;if(e.state===k)return I(e);if(e.suspense&&A(e.suspense.inFallback))return e.suspense.effects.push(e);const t=[e];for(;(e=e.owner)&&(!e.updatedAt||e.updatedAt<B);)e.state&&t.push(e);for(let n=t.length-1;n>=0;n--)if(e=t[n],e.state===x)O(e);else if(e.state===k){const i=b;b=null,E(()=>I(e,t[0]),!1),b=i}}function E(e,t){if(b)return e();let n=!1;t||(b=[]),w?n=!0:w=[],B++;try{const i=e();return pe(n),i}catch(i){n||(w=null),b=null,le(i)}}function pe(e){if(b&&(re(b),b=null),e)return;const t=w;w=null,t.length&&E(()=>te(t),!1)}function re(e){for(let t=0;t<e.length;t++)M(e[t])}function ve(e){let t,n=0;for(t=0;t<e.length;t++){const i=e[t];i.user?e[n++]=i:M(i)}for(t=0;t<n;t++)M(e[t])}function I(e,t){e.state=0;for(let n=0;n<e.sources.length;n+=1){const i=e.sources[n];if(i.sources){const r=i.state;r===x?i!==t&&(!i.updatedAt||i.updatedAt<B)&&M(i):r===k&&I(i,t)}}}function oe(e){for(let t=0;t<e.observers.length;t+=1){const n=e.observers[t];n.state||(n.state=k,n.pure?b.push(n):w.push(n),n.observers&&oe(n))}}function S(e){let t;if(e.sources)for(;e.sources.length;){const n=e.sources.pop(),i=e.sourceSlots.pop(),r=n.observers;if(r&&r.length){const o=r.pop(),l=n.observerSlots.pop();i<r.length&&(o.sourceSlots[l]=i,r[i]=o,n.observerSlots[i]=l)}}if(e.tOwned){for(t=e.tOwned.length-1;t>=0;t--)S(e.tOwned[t]);delete e.tOwned}if(e.owned){for(t=e.owned.length-1;t>=0;t--)S(e.owned[t]);e.owned=null}if(e.cleanups){for(t=e.cleanups.length-1;t>=0;t--)e.cleanups[t]();e.cleanups=null}e.state=0}function we(e){return e instanceof Error?e:new Error(typeof e=="string"?e:"Unknown error",{cause:e})}function le(e,t=p){throw we(e)}function T(e,t){return A(()=>e(t||{}))}function N(){return!0}const F={get(e,t,n){return t===L?n:e.get(t)},has(e,t){return t===L?!0:e.has(t)},set:N,deleteProperty:N,getOwnPropertyDescriptor(e,t){return{configurable:!0,enumerable:!0,get(){return e.get(t)},set:N,deleteProperty:N}},ownKeys(e){return e.keys()}};function z(e){return(e=typeof e=="function"?e():e)?e:{}}function xe(){for(let e=0,t=this.length;e<t;++e){const n=this[e]();if(n!==void 0)return n}}function $e(...e){let t=!1;for(let l=0;l<e.length;l++){const s=e[l];t=t||!!s&&L in s,e[l]=typeof s=="function"?(t=!0,ye(s)):s}if(ee&&t)return new Proxy({get(l){for(let s=e.length-1;s>=0;s--){const a=z(e[s])[l];if(a!==void 0)return a}},has(l){for(let s=e.length-1;s>=0;s--)if(l in z(e[s]))return!0;return!1},keys(){const l=[];for(let s=0;s<e.length;s++)l.push(...Object.keys(z(e[s])));return[...new Set(l)]}},F);const n={},i=Object.create(null);for(let l=e.length-1;l>=0;l--){const s=e[l];if(!s)continue;const a=Object.getOwnPropertyNames(s);for(let f=a.length-1;f>=0;f--){const u=a[f];if(u==="__proto__"||u==="constructor")continue;const d=Object.getOwnPropertyDescriptor(s,u);if(!i[u])i[u]=d.get?{enumerable:!0,configurable:!0,get:xe.bind(n[u]=[d.get.bind(s)])}:d.value!==void 0?d:void 0;else{const c=n[u];c&&(d.get?c.push(d.get.bind(s)):d.value!==void 0&&c.push(()=>d.value))}}}const r={},o=Object.keys(i);for(let l=o.length-1;l>=0;l--){const s=o[l],a=i[s];a&&a.get?Object.defineProperty(r,s,a):r[s]=a?a.value:void 0}return r}function ae(e,...t){if(ee&&L in e){const r=new Set(t.length>1?t.flat():t[0]),o=t.map(l=>new Proxy({get(s){return l.includes(s)?e[s]:void 0},has(s){return l.includes(s)&&s in e},keys(){return l.filter(s=>s in e)}},F));return o.push(new Proxy({get(l){return r.has(l)?void 0:e[l]},has(l){return r.has(l)?!1:l in e},keys(){return Object.keys(e).filter(l=>!r.has(l))}},F)),o}const n={},i=t.map(()=>({}));for(const r of Object.getOwnPropertyNames(e)){const o=Object.getOwnPropertyDescriptor(e,r),l=!o.get&&!o.set&&o.enumerable&&o.writable&&o.configurable;let s=!1,a=0;for(const f of t)f.includes(r)&&(s=!0,l?i[a][r]=o.value:Object.defineProperty(i[a],r,o)),++a;s||(l?n[r]=o.value:Object.defineProperty(n,r,o))}return[...i,n]}const Pe=["allowfullscreen","async","autofocus","autoplay","checked","controls","default","disabled","formnovalidate","hidden","indeterminate","inert","ismap","loop","multiple","muted","nomodule","novalidate","open","playsinline","readonly","required","reversed","seamless","selected"],_e=new Set(["className","value","readOnly","noValidate","formNoValidate","isMap","noModule","playsInline",...Pe]),Ce=new Set(["innerHTML","textContent","innerText","children"]),Se=Object.assign(Object.create(null),{className:"class",htmlFor:"for"}),Ae=Object.assign(Object.create(null),{class:"className",novalidate:{$:"noValidate",FORM:1},formnovalidate:{$:"formNoValidate",BUTTON:1,INPUT:1},ismap:{$:"isMap",IMG:1},nomodule:{$:"noModule",SCRIPT:1},playsinline:{$:"playsInline",VIDEO:1},readonly:{$:"readOnly",INPUT:1,TEXTAREA:1}});function Oe(e,t){const n=Ae[e];return typeof n=="object"?n[t]?n.$:void 0:n}const Ee=new Set(["beforeinput","click","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"]);function Ne(e,t,n){let i=n.length,r=t.length,o=i,l=0,s=0,a=t[r-1].nextSibling,f=null;for(;l<r||s<o;){if(t[l]===n[s]){l++,s++;continue}for(;t[r-1]===n[o-1];)r--,o--;if(r===l){const u=o<i?s?n[s-1].nextSibling:n[o-s]:a;for(;s<o;)e.insertBefore(n[s++],u)}else if(o===s)for(;l<r;)(!f||!f.has(t[l]))&&t[l].remove(),l++;else if(t[l]===n[o-1]&&n[s]===t[r-1]){const u=t[--r].nextSibling;e.insertBefore(n[s++],t[l++].nextSibling),e.insertBefore(n[--o],u),t[r]=n[o]}else{if(!f){f=new Map;let d=s;for(;d<o;)f.set(n[d],d++)}const u=f.get(t[l]);if(u!=null)if(s<u&&u<o){let d=l,c=1,h;for(;++d<r&&d<o&&!((h=f.get(t[d]))==null||h!==u+c);)c++;if(c>u-s){const g=t[l];for(;s<u;)e.insertBefore(n[s++],g)}else e.replaceChild(n[s++],t[l++])}else l++;else t[l++].remove()}}}const q="_$DX_DELEGATE";function Te(e,t,n,i={}){let r;return he(o=>{r=o,t===document?e():v(t,e(),t.firstChild?null:void 0,n)},i.owner),()=>{r(),t.textContent=""}}function P(e,t,n,i){let r;const o=()=>{const s=document.createElement("template");return s.innerHTML=e,s.content.firstChild},l=()=>(r||(r=o())).cloneNode(!0);return l.cloneNode=l,l}function Le(e,t=window.document){const n=t[q]||(t[q]=new Set);for(let i=0,r=e.length;i<r;i++){const o=e[i];n.has(o)||(n.add(o),t.addEventListener(o,Ve))}}function K(e,t,n){n==null?e.removeAttribute(t):e.setAttribute(t,n)}function je(e,t,n){n?e.setAttribute(t,""):e.removeAttribute(t)}function C(e,t){t==null?e.removeAttribute("class"):e.className=t}function ke(e,t,n,i){if(i)Array.isArray(n)?(e[`$$${t}`]=n[0],e[`$$${t}Data`]=n[1]):e[`$$${t}`]=n;else if(Array.isArray(n)){const r=n[0];e.addEventListener(t,n[0]=o=>r.call(e,n[1],o))}else e.addEventListener(t,n,typeof n!="function"&&n)}function Me(e,t,n={}){const i=Object.keys(t||{}),r=Object.keys(n);let o,l;for(o=0,l=r.length;o<l;o++){const s=r[o];!s||s==="undefined"||t[s]||(Y(e,s,!1),delete n[s])}for(o=0,l=i.length;o<l;o++){const s=i[o],a=!!t[s];!s||s==="undefined"||n[s]===a||!a||(Y(e,s,!0),n[s]=a)}return n}function Ie(e,t,n){if(!t)return n?K(e,"style"):t;const i=e.style;if(typeof t=="string")return i.cssText=t;typeof n=="string"&&(i.cssText=n=void 0),n||(n={}),t||(t={});let r,o;for(o in n)t[o]==null&&i.removeProperty(o),delete n[o];for(o in t)r=t[o],r!==n[o]&&(i.setProperty(o,r),n[o]=r);return n}function ce(e,t={},n,i){const r={};return $(()=>typeof t.ref=="function"&&De(t.ref,e)),$(()=>Be(e,t,n,!0,r,!0)),r}function De(e,t,n){return A(()=>e(t,n))}function v(e,t,n,i){if(n!==void 0&&!i&&(i=[]),typeof t!="function")return D(e,t,i,n);$(r=>D(e,t(),r,n),i)}function Be(e,t,n,i,r={},o=!1){t||(t={});for(const l in r)if(!(l in t)){if(l==="children")continue;r[l]=J(e,l,null,r[l],n,o,t)}for(const l in t){if(l==="children")continue;const s=t[l];r[l]=J(e,l,s,r[l],n,o,t)}}function Ue(e){return e.toLowerCase().replace(/-([a-z])/g,(t,n)=>n.toUpperCase())}function Y(e,t,n){const i=t.trim().split(/\s+/);for(let r=0,o=i.length;r<o;r++)e.classList.toggle(i[r],n)}function J(e,t,n,i,r,o,l){let s,a,f,u,d;if(t==="style")return Ie(e,n,i);if(t==="classList")return Me(e,n,i);if(n===i)return i;if(t==="ref")o||n(e);else if(t.slice(0,3)==="on:"){const c=t.slice(3);i&&e.removeEventListener(c,i,typeof i!="function"&&i),n&&e.addEventListener(c,n,typeof n!="function"&&n)}else if(t.slice(0,10)==="oncapture:"){const c=t.slice(10);i&&e.removeEventListener(c,i,!0),n&&e.addEventListener(c,n,!0)}else if(t.slice(0,2)==="on"){const c=t.slice(2).toLowerCase(),h=Ee.has(c);if(!h&&i){const g=Array.isArray(i)?i[0]:i;e.removeEventListener(c,g)}(h||n)&&(ke(e,c,n,h),h&&Le([c]))}else t.slice(0,5)==="attr:"?K(e,t.slice(5),n):t.slice(0,5)==="bool:"?je(e,t.slice(5),n):(d=t.slice(0,5)==="prop:")||(f=Ce.has(t))||(u=Oe(t,e.tagName))||(a=_e.has(t))||(s=e.nodeName.includes("-")||"is"in l)?(d&&(t=t.slice(5),a=!0),t==="class"||t==="className"?C(e,n):s&&!a&&!f?e[Ue(t)]=n:e[u||t]=n):K(e,Se[t]||t,n);return n}function Ve(e){let t=e.target;const n=`$$${e.type}`,i=e.target,r=e.currentTarget,o=a=>Object.defineProperty(e,"target",{configurable:!0,value:a}),l=()=>{const a=t[n];if(a&&!t.disabled){const f=t[`${n}Data`];if(f!==void 0?a.call(t,f,e):a.call(t,e),e.cancelBubble)return}return t.host&&typeof t.host!="string"&&!t.host._$host&&t.contains(e.target)&&o(t.host),!0},s=()=>{for(;l()&&(t=t._$host||t.parentNode||t.host););};if(Object.defineProperty(e,"currentTarget",{configurable:!0,get(){return t||document}}),e.composedPath){const a=e.composedPath();o(a[0]);for(let f=0;f<a.length-2&&(t=a[f],!!l());f++){if(t._$host){t=t._$host,s();break}if(t.parentNode===r)break}}else s();o(i)}function D(e,t,n,i,r){for(;typeof n=="function";)n=n();if(t===n)return n;const o=typeof t,l=i!==void 0;if(e=l&&n[0]&&n[0].parentNode||e,o==="string"||o==="number"){if(o==="number"&&(t=t.toString(),t===n))return n;if(l){let s=n[0];s&&s.nodeType===3?s.data!==t&&(s.data=t):s=document.createTextNode(t),n=_(e,n,i,s)}else n!==""&&typeof n=="string"?n=e.firstChild.data=t:n=e.textContent=t}else if(t==null||o==="boolean")n=_(e,n,i);else{if(o==="function")return $(()=>{let s=t();for(;typeof s=="function";)s=s();n=D(e,s,n,i)}),()=>n;if(Array.isArray(t)){const s=[],a=n&&Array.isArray(n);if(Q(s,t,n,r))return $(()=>n=D(e,s,n,i,!0)),()=>n;if(s.length===0){if(n=_(e,n,i),l)return n}else a?n.length===0?W(e,s,i):Ne(e,n,s):(n&&_(e),W(e,s));n=s}else if(t.nodeType){if(Array.isArray(n)){if(l)return n=_(e,n,i,t);_(e,n,null,t)}else n==null||n===""||!e.firstChild?e.appendChild(t):e.replaceChild(t,e.firstChild);n=t}}return n}function Q(e,t,n,i){let r=!1;for(let o=0,l=t.length;o<l;o++){let s=t[o],a=n&&n[e.length],f;if(!(s==null||s===!0||s===!1))if((f=typeof s)=="object"&&s.nodeType)e.push(s);else if(Array.isArray(s))r=Q(e,s,a)||r;else if(f==="function")if(i){for(;typeof s=="function";)s=s();r=Q(e,Array.isArray(s)?s:[s],Array.isArray(a)?a:[a])||r}else e.push(s),r=!0;else{const u=String(s);a&&a.nodeType===3&&a.data===u?e.push(a):e.push(document.createTextNode(u))}}return r}function W(e,t,n=null){for(let i=0,r=t.length;i<r;i++)e.insertBefore(t[i],n)}function _(e,t,n,i){if(n===void 0)return e.textContent="";const r=i||document.createTextNode("");if(t.length){let o=!1;for(let l=t.length-1;l>=0;l--){const s=t[l];if(r!==s){const a=s.parentNode===e;!o&&!l?a?e.replaceChild(r,s):e.insertBefore(r,n):a&&s.remove()}else o=!0}}else e.insertBefore(r,n);return[r]}var Re=P('<button><div class="relative z-10"></div><div class="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-300"><div class="absolute top-1/2 left-1/2 w-0 h-0 bg-white/20 rounded-full animate-ping transform -translate-x-1/2 -translate-y-1/2">');const Z=e=>{const[t,n]=ae(e,["variant","size","glow","physics","children","class"]),i=()=>{switch(t.variant){case"cosmic":return"bg-gradient-to-r from-cosmic-500 to-cosmic-600 hover:from-cosmic-600 hover:to-cosmic-700 text-white";case"golden":return"bg-gradient-to-r from-golden-500 to-golden-600 hover:from-golden-600 hover:to-golden-700 text-white";case"divine":return"bg-gradient-to-r from-divine-500 to-divine-600 hover:from-divine-600 hover:to-divine-700 text-white";case"glass":return"bg-white/10 backdrop-blur-lg border border-white/20 hover:bg-white/20 text-white";default:return"bg-gradient-to-r from-cosmic-500 to-divine-500 hover:from-cosmic-600 hover:to-divine-600 text-white"}},r=()=>{switch(t.size){case"sm":return"px-golden-sm py-golden-xs text-sm";case"md":return"px-golden-md py-golden-sm text-base";case"lg":return"px-golden-lg py-golden-md text-lg";case"xl":return"px-golden-xl py-golden-lg text-xl";default:return"px-golden-md py-golden-sm text-base"}},o=()=>{if(!t.glow)return"";switch(t.variant){case"cosmic":return"hover:shadow-[0_0_20px_rgba(14,165,233,0.5)]";case"golden":return"hover:shadow-[0_0_20px_rgba(245,158,11,0.5)]";case"divine":return"hover:shadow-[0_0_20px_rgba(217,70,239,0.5)]";default:return"hover:shadow-[0_0_20px_rgba(14,165,233,0.5)]"}},l=`
    relative overflow-hidden rounded-lg font-medium
    transform transition-all duration-300 ease-out
    hover:scale-105 active:scale-95
    focus:outline-none focus:ring-2 focus:ring-white/50
    cursor-pointer select-none
    ${i()}
    ${r()}
    ${o()}
    ${t.physics?"hover:animate-bounce active:animate-pulse":""}
    ${t.class||""}
  `.trim().replace(/\s+/g," ");return(()=>{var s=Re(),a=s.firstChild;return C(s,l),ce(s,n,!1),v(a,()=>t.children),s})()};var Ge=P('<div><div class="absolute inset-0 opacity-5"><div class="absolute top-0 left-0 w-full h-full"><div class="grid grid-cols-8 grid-rows-5 h-full"></div></div></div><div class="relative z-10"></div><div class="absolute inset-0 pointer-events-none">'),ze=P('<div class="border border-white/10">'),Fe=P('<div class="absolute w-1 h-1 bg-white/20 rounded-full animate-pulse">');const Ke=e=>{const[t,n]=ae(e,["variant","size","glow","physics","hover3d","children","class"]),i=()=>{switch(t.variant){case"glass":return"bg-white/10 backdrop-blur-lg border border-white/20";case"cosmic":return"bg-gradient-to-br from-cosmic-500/20 to-cosmic-600/20 backdrop-blur-lg border border-cosmic-300/30";case"golden":return"bg-gradient-to-br from-golden-500/20 to-golden-600/20 backdrop-blur-lg border border-golden-300/30";case"divine":return"bg-gradient-to-br from-divine-500/20 to-divine-600/20 backdrop-blur-lg border border-divine-300/30";default:return"bg-white/10 backdrop-blur-lg border border-white/20"}},r=()=>{switch(t.size){case"sm":return"p-golden-sm rounded-lg";case"md":return"p-golden-md rounded-xl";case"lg":return"p-golden-lg rounded-2xl";case"xl":return"p-golden-xl rounded-3xl";default:return"p-golden-md rounded-xl"}},o=()=>{if(!t.glow)return"";switch(t.variant){case"cosmic":return"hover:shadow-[0_0_30px_rgba(14,165,233,0.3)]";case"golden":return"hover:shadow-[0_0_30px_rgba(245,158,11,0.3)]";case"divine":return"hover:shadow-[0_0_30px_rgba(217,70,239,0.3)]";default:return"hover:shadow-[0_0_30px_rgba(255,255,255,0.2)]"}},l=`
    relative overflow-hidden
    transition-all duration-500 ease-out
    hover:border-white/40
    ${t.physics?"hover:scale-105 hover:rotate-1":""}
    ${t.hover3d?"transform-gpu perspective-1000":""}
    ${i()}
    ${r()}
    ${o()}
    ${t.class||""}
  `.trim().replace(/\s+/g," ");return(()=>{var s=Ge(),a=s.firstChild,f=a.firstChild,u=f.firstChild,d=a.nextSibling,c=d.nextSibling;return C(s,l),ce(s,$e({get style(){return{"transform-style":t.hover3d?"preserve-3d":"flat",perspective:t.hover3d?"1000px":"none"}}},n),!1),v(u,()=>Array.from({length:40},(h,g)=>(()=>{var m=ze();return`${g*.1}s`!=null?m.style.setProperty("animation-delay",`${g*.1}s`):m.style.removeProperty("animation-delay"),m})())),v(d,()=>t.children),v(c,()=>Array.from({length:5},(h,g)=>(()=>{var m=Fe();return`${20+g*15}%`!=null?m.style.setProperty("top",`${20+g*15}%`):m.style.removeProperty("top"),`${10+g*20}%`!=null?m.style.setProperty("left",`${10+g*20}%`):m.style.removeProperty("left"),`${g*.5}s`!=null?m.style.setProperty("animation-delay",`${g*.5}s`):m.style.removeProperty("animation-delay"),`${2+g*.5}s`!=null?m.style.setProperty("animation-duration",`${2+g*.5}s`):m.style.removeProperty("animation-duration"),m})())),s})()};var Qe=P('<div class=text-center><h2 class="text-2xl font-bold mb-golden-md text-white">Cosmic Counter: <span class=text-golden-300></span></h2><div class="flex gap-golden-sm justify-center">'),He=P('<div class="min-h-screen bg-gradient-to-br from-cosmic-500 via-divine-500 to-golden-500 relative overflow-hidden"><div class="absolute inset-0 pointer-events-none"><div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 opacity-10"><svg width=400 height=400 viewBox="0 0 400 400"><path d="M200,200 Q300,200 300,100 Q300,0 200,0 Q100,0 100,100 Q100,200 200,200"fill=none stroke=white stroke-width=2 class=animate-pulse></path></svg></div></div><div><div><h1 class="text-6xl md:text-8xl font-bold mb-golden-md"><span class="bg-gradient-to-r from-white via-golden-300 to-cosmic-300 bg-clip-text text-transparent">HVAC</span><br><span class="bg-gradient-to-r from-divine-300 via-cosmic-300 to-golden-300 bg-clip-text text-transparent">Cosmic CRM</span></h1><p class="text-xl md:text-2xl text-white/80 max-w-2xl mx-auto leading-relaxed">Experience the divine harmony of <span class="text-golden-300 font-semibold">Golden Ratio</span> design with <span class="text-cosmic-300 font-semibold">137 cosmic libraries</span> and <span class="text-divine-300 font-semibold">infinite possibilities</span></p></div><div><p class=text-sm>Crafted with 💫 using SolidJS, Golden Ratio, and cosmic energy'),Xe=P('<div class="absolute rounded-full bg-white/10 backdrop-blur-sm animate-pulse">');const qe=()=>{const[e,t]=X(0),[n,i]=X(!1);return me(()=>{setTimeout(()=>i(!0),100)}),(()=>{var r=He(),o=r.firstChild,l=o.firstChild,s=o.nextSibling,a=s.firstChild,f=a.nextSibling;return v(o,()=>Array.from({length:20},(u,d)=>(()=>{var c=Xe();return`${d*.2}s`!=null?c.style.setProperty("animation-delay",`${d*.2}s`):c.style.removeProperty("animation-delay"),$(h=>{var g=`${Math.random()*100+20}px`,m=`${Math.random()*100+20}px`,U=`${Math.random()*100}%`,V=`${Math.random()*100}%`,R=`${3+Math.random()*2}s`;return g!==h.e&&((h.e=g)!=null?c.style.setProperty("width",g):c.style.removeProperty("width")),m!==h.t&&((h.t=m)!=null?c.style.setProperty("height",m):c.style.removeProperty("height")),U!==h.a&&((h.a=U)!=null?c.style.setProperty("top",U):c.style.removeProperty("top")),V!==h.o&&((h.o=V)!=null?c.style.setProperty("left",V):c.style.removeProperty("left")),R!==h.i&&((h.i=R)!=null?c.style.setProperty("animation-duration",R):c.style.removeProperty("animation-duration")),h},{e:void 0,t:void 0,a:void 0,o:void 0,i:void 0}),c})()),l),v(s,T(Ke,{variant:"glass",size:"lg",glow:!0,class:"mb-golden-lg",get children(){var u=Qe(),d=u.firstChild,c=d.firstChild,h=c.nextSibling,g=d.nextSibling;return v(h,e),v(g,T(Z,{variant:"cosmic",size:"lg",glow:!0,physics:!0,onClick:()=>t(e()+1),children:"Increment ✨"}),null),v(g,T(Z,{variant:"divine",size:"lg",glow:!0,physics:!0,onClick:()=>t(0),children:"Reset 🌟"}),null),u}}),f),$(u=>{var d=`relative z-10 container mx-auto px-4 py-8 min-h-screen flex flex-col items-center justify-center transition-all duration-1000 ${n()?"opacity-100 translate-y-0":"opacity-0 translate-y-12"}`,c=`text-center mb-golden-xl transition-all duration-800 delay-500 ${n()?"opacity-100 scale-100":"opacity-0 scale-95"}`,h=`text-center text-white/60 transition-opacity duration-1000 delay-1500 ${n()?"opacity-100":"opacity-0"}`;return d!==u.e&&C(s,u.e=d),c!==u.t&&C(a,u.t=c),h!==u.a&&C(f,u.a=h),u},{e:void 0,t:void 0,a:void 0}),r})()},Ye=document.getElementById("root");Te(()=>T(qe,{}),Ye);
