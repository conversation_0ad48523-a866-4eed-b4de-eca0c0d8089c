/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Golden Ratio System (1.618)
      spacing: {
        'golden-xs': '0.382rem',    // 1/φ²
        'golden-sm': '0.618rem',    // 1/φ
        'golden-base': '1rem',      // 1
        'golden-md': '1.618rem',    // φ
        'golden-lg': '2.618rem',    // φ²
        'golden-xl': '4.236rem',    // φ³
        'golden-2xl': '6.854rem',   // φ⁴
        'golden-3xl': '11.09rem',   // φ⁵
        'golden-4xl': '17.944rem',  // φ⁶
        // Fibonacci Sequence Spacing (merged)
        'fib-1': '0.25rem',   // 1
        'fib-2': '0.5rem',    // 2
        'fib-3': '0.75rem',   // 3
        'fib-5': '1.25rem',   // 5
        'fib-8': '2rem',      // 8
        'fib-13': '3.25rem',  // 13
        'fib-21': '5.25rem',  // 21
        'fib-34': '8.5rem',   // 34
        'fib-55': '13.75rem', // 55
        'fib-89': '22.25rem', // 89
        'fib-144': '36rem',   // 144
      },

      // Golden Ratio Typography
      fontSize: {
        'golden-xs': ['0.618rem', { lineHeight: '1rem' }],
        'golden-sm': ['0.875rem', { lineHeight: '1.414rem' }],
        'golden-base': ['1rem', { lineHeight: '1.618rem' }],
        'golden-lg': ['1.125rem', { lineHeight: '1.82rem' }],
        'golden-xl': ['1.25rem', { lineHeight: '2.023rem' }],
        'golden-2xl': ['1.5rem', { lineHeight: '2.427rem' }],
        'golden-3xl': ['1.875rem', { lineHeight: '3.034rem' }],
        'golden-4xl': ['2.25rem', { lineHeight: '3.641rem' }],
        'golden-5xl': ['3rem', { lineHeight: '4.854rem' }],
        'golden-6xl': ['3.75rem', { lineHeight: '6.068rem' }],
        'golden-7xl': ['4.5rem', { lineHeight: '7.281rem' }],
        'golden-8xl': ['6rem', { lineHeight: '9.708rem' }],
        'golden-9xl': ['8rem', { lineHeight: '12.944rem' }],
      },

      // Cosmic Color Palette
      colors: {
        cosmic: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
          950: '#082f49',
        },
        golden: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
          950: '#451a03',
        },
        divine: {
          50: '#fdf4ff',
          100: '#fae8ff',
          200: '#f5d0fe',
          300: '#f0abfc',
          400: '#e879f9',
          500: '#d946ef',
          600: '#c026d3',
          700: '#a21caf',
          800: '#86198f',
          900: '#701a75',
          950: '#4a044e',
        }
      },

      // Golden Ratio Animations
      animation: {
        'golden-pulse': 'golden-pulse 1.618s ease-in-out infinite',
        'golden-bounce': 'golden-bounce 1.618s infinite',
        'golden-spin': 'golden-spin 2.618s linear infinite',
        'cosmic-float': 'cosmic-float 4.236s ease-in-out infinite',
        'divine-glow': 'divine-glow 3.236s ease-in-out infinite alternate',
      },

      keyframes: {
        'golden-pulse': {
          '0%, 100%': { opacity: '1' },
          '50%': { opacity: '0.618' },
        },
        'golden-bounce': {
          '0%, 100%': { 
            transform: 'translateY(-25%)',
            animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
          },
          '50%': { 
            transform: 'none',
            animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
          },
        },
        'golden-spin': {
          'from': { transform: 'rotate(0deg)' },
          'to': { transform: 'rotate(360deg)' },
        },
        'cosmic-float': {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        'divine-glow': {
          '0%': { boxShadow: '0 0 5px #d946ef, 0 0 10px #d946ef, 0 0 15px #d946ef' },
          '100%': { boxShadow: '0 0 10px #d946ef, 0 0 20px #d946ef, 0 0 30px #d946ef' },
        },
      },

      // Golden Ratio Aspect Ratios
      aspectRatio: {
        'golden': '1.618',
        'golden-inverse': '0.618',
      },

      // Cosmic Gradients
      backgroundImage: {
        'cosmic-gradient': 'linear-gradient(135deg, #0ea5e9 0%, #d946ef 100%)',
        'golden-gradient': 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        'divine-gradient': 'linear-gradient(135deg, #d946ef 0%, #a21caf 100%)',
        'cosmic-radial': 'radial-gradient(circle, #0ea5e9 0%, #d946ef 100%)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
  ],
}
