{"name": "hvac-solidjs-cosmic", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@kobalte/core": "^0.13.9", "@modular-forms/solid": "^0.25.1", "@motionone/solid": "^10.16.4", "@solidjs/router": "^0.15.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/typography": "^0.5.16", "@tanstack/solid-query": "^5.77.2", "@types/three": "^0.176.0", "animejs": "^4.0.2", "autoprefixer": "^10.4.21", "cannon-es": "^0.20.0", "chroma-js": "^3.1.2", "gsap": "^3.13.0", "lottie-web": "^5.13.0", "lucide-solid": "^0.511.0", "matter-js": "^0.20.0", "postcss": "^8.5.3", "solid-js": "^1.9.5", "tailwindcss": "^4.1.8", "three": "^0.176.0", "zod": "^3.25.32"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^6.3.5", "vite-plugin-solid": "^2.11.6"}}