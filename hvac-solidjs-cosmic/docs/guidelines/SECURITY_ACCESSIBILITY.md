# 🔒 SECURITY & ACCESSIBILITY GUIDELINES
## *Building Secure, Inclusive Cosmic Interfaces*

---

## 🛡️ **SECURITY PRINCIPLES**

### **Core Security Philosophy**
```
"Security is not an afterthought - it's the foundation upon which 
our cosmic interface stands. Every component, every interaction, 
every data flow must be designed with security in mind."
```

### **Security by Design**
1. **Principle of Least Privilege** - Grant minimum necessary permissions
2. **Defense in Depth** - Multiple layers of security
3. **Fail Securely** - Default to secure state on failure
4. **Zero Trust** - Never trust, always verify
5. **Privacy by Design** - Protect user data from the start

---

## 🔐 **INPUT VALIDATION & SANITIZATION**

### **Client-Side Validation**
```typescript
import { z } from 'zod'

// Zod schemas for type-safe validation
export const CustomerSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Name contains invalid characters'),
  
  email: z.string()
    .email('Invalid email format')
    .max(254, 'Email too long'),
  
  phone: z.string()
    .regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone format')
    .optional(),
  
  address: z.string()
    .max(500, 'Address too long')
    .optional()
})

// Validation helper
export const validateInput = <T>(schema: z.ZodSchema<T>, data: unknown): T => {
  try {
    return schema.parse(data)
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError(error.errors)
    }
    throw error
  }
}
```

### **XSS Prevention**
```typescript
// HTML sanitization
import DOMPurify from 'dompurify'

export const sanitizeHTML = (dirty: string): string => {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: []
  })
}

// Safe innerHTML in SolidJS
export const SafeHTML: Component<{ content: string }> = (props) => {
  const sanitizedContent = () => sanitizeHTML(props.content)
  
  return <div innerHTML={sanitizedContent()} />
}

// Content Security Policy headers
export const CSP_HEADER = `
  default-src 'self';
  script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net;
  style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
  font-src 'self' https://fonts.gstatic.com;
  img-src 'self' data: https:;
  connect-src 'self' https://api.example.com;
  frame-ancestors 'none';
  base-uri 'self';
  form-action 'self';
`.replace(/\s+/g, ' ').trim()
```

---

## 🔑 **AUTHENTICATION & AUTHORIZATION**

### **JWT Token Management**
```typescript
// Secure token storage
export class TokenManager {
  private static readonly TOKEN_KEY = 'cosmic_auth_token'
  private static readonly REFRESH_KEY = 'cosmic_refresh_token'
  
  static setTokens(accessToken: string, refreshToken: string): void {
    // Store in httpOnly cookies (server-side) or secure storage
    document.cookie = `${this.TOKEN_KEY}=${accessToken}; Secure; HttpOnly; SameSite=Strict`
    document.cookie = `${this.REFRESH_KEY}=${refreshToken}; Secure; HttpOnly; SameSite=Strict`
  }
  
  static getAccessToken(): string | null {
    // In production, this would be handled server-side
    return this.getCookie(this.TOKEN_KEY)
  }
  
  static clearTokens(): void {
    document.cookie = `${this.TOKEN_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
    document.cookie = `${this.REFRESH_KEY}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`
  }
  
  private static getCookie(name: string): string | null {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop()?.split(';').shift() || null
    return null
  }
}

// Role-based access control
export const usePermissions = () => {
  const [user] = useAuth()
  
  const hasPermission = (permission: string): boolean => {
    return user()?.permissions?.includes(permission) ?? false
  }
  
  const hasRole = (role: string): boolean => {
    return user()?.roles?.includes(role) ?? false
  }
  
  return { hasPermission, hasRole }
}

// Protected route component
export const ProtectedRoute: Component<{
  children: JSX.Element
  requiredPermission?: string
  requiredRole?: string
}> = (props) => {
  const { hasPermission, hasRole } = usePermissions()
  
  const isAuthorized = () => {
    if (props.requiredPermission && !hasPermission(props.requiredPermission)) {
      return false
    }
    if (props.requiredRole && !hasRole(props.requiredRole)) {
      return false
    }
    return true
  }
  
  return (
    <Show when={isAuthorized()} fallback={<UnauthorizedPage />}>
      {props.children}
    </Show>
  )
}
```

---

## 🌐 **ACCESSIBILITY (A11Y)**

### **WCAG 2.1 AA Compliance**

#### **Semantic HTML Structure**
```tsx
// ✅ Good: Semantic structure
export const CustomerList: Component = () => {
  return (
    <main>
      <header>
        <h1>Customer Management</h1>
        <nav aria-label="Customer actions">
          <button type="button" aria-describedby="add-customer-help">
            Add Customer
          </button>
          <div id="add-customer-help" class="sr-only">
            Opens a form to add a new customer to the system
          </div>
        </nav>
      </header>
      
      <section aria-labelledby="customer-list-heading">
        <h2 id="customer-list-heading">Customer List</h2>
        <table role="table" aria-label="Customer information">
          <thead>
            <tr>
              <th scope="col">Name</th>
              <th scope="col">Email</th>
              <th scope="col">Actions</th>
            </tr>
          </thead>
          <tbody>
            <For each={customers()}>
              {(customer) => (
                <tr>
                  <td>{customer.name}</td>
                  <td>{customer.email}</td>
                  <td>
                    <button 
                      type="button"
                      aria-label={`Edit ${customer.name}`}
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              )}
            </For>
          </tbody>
        </table>
      </section>
    </main>
  )
}
```

#### **Keyboard Navigation**
```css
/* Focus management */
.focus-visible {
  outline: 2px solid var(--cosmic-500);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Skip links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--cosmic-500);
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Focus trap for modals */
.modal {
  /* Ensure focus stays within modal */
}
```

```typescript
// Keyboard navigation helper
export const useKeyboardNavigation = (
  containerRef: () => HTMLElement | undefined,
  itemSelector: string
) => {
  const handleKeyDown = (event: KeyboardEvent) => {
    const container = containerRef()
    if (!container) return
    
    const items = Array.from(container.querySelectorAll(itemSelector))
    const currentIndex = items.indexOf(document.activeElement as HTMLElement)
    
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        const nextIndex = (currentIndex + 1) % items.length
        ;(items[nextIndex] as HTMLElement)?.focus()
        break
        
      case 'ArrowUp':
        event.preventDefault()
        const prevIndex = (currentIndex - 1 + items.length) % items.length
        ;(items[prevIndex] as HTMLElement)?.focus()
        break
        
      case 'Home':
        event.preventDefault()
        ;(items[0] as HTMLElement)?.focus()
        break
        
      case 'End':
        event.preventDefault()
        ;(items[items.length - 1] as HTMLElement)?.focus()
        break
    }
  }
  
  createEffect(() => {
    const container = containerRef()
    if (container) {
      container.addEventListener('keydown', handleKeyDown)
      onCleanup(() => container.removeEventListener('keydown', handleKeyDown))
    }
  })
}
```

#### **Screen Reader Support**
```typescript
// Live regions for dynamic content
export const LiveRegion: Component<{
  children: JSX.Element
  politeness?: 'polite' | 'assertive'
}> = (props) => {
  return (
    <div
      aria-live={props.politeness || 'polite'}
      aria-atomic="true"
      class="sr-only"
    >
      {props.children}
    </div>
  )
}

// Accessible form component
export const AccessibleForm: Component<{
  onSubmit: (data: FormData) => void
  children: JSX.Element
}> = (props) => {
  const [errors, setErrors] = createSignal<Record<string, string>>({})
  
  return (
    <form
      onSubmit={(e) => {
        e.preventDefault()
        const formData = new FormData(e.currentTarget)
        props.onSubmit(formData)
      }}
      novalidate
    >
      {props.children}
      
      <Show when={Object.keys(errors()).length > 0}>
        <div role="alert" aria-label="Form errors">
          <h3>Please correct the following errors:</h3>
          <ul>
            <For each={Object.entries(errors())}>
              {([field, error]) => (
                <li>
                  <a href={`#${field}`}>{error}</a>
                </li>
              )}
            </For>
          </ul>
        </div>
      </Show>
    </form>
  )
}
```

#### **Color Contrast & Visual Design**
```css
/* WCAG AA compliant color ratios */
:root {
  /* Text on backgrounds - minimum 4.5:1 ratio */
  --text-primary: #1f2937;      /* 16.94:1 on white */
  --text-secondary: #4b5563;    /* 7.07:1 on white */
  --text-tertiary: #6b7280;     /* 4.69:1 on white */
  
  /* Interactive elements - minimum 3:1 ratio */
  --link-color: #0369a1;        /* 5.93:1 on white */
  --button-primary: #0ea5e9;    /* 3.36:1 on white */
  
  /* Error states */
  --error-color: #dc2626;       /* 5.25:1 on white */
  --error-bg: #fef2f2;          /* Light error background */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #000000;
    --bg-primary: #ffffff;
    --border-color: #000000;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
```

---

## 🔍 **SECURITY TESTING**

### **Automated Security Scanning**
```typescript
// Security test utilities
export const securityTests = {
  // XSS vulnerability testing
  testXSSPrevention: (component: Component, maliciousInput: string) => {
    const { container } = render(() => component({ input: maliciousInput }))
    
    // Check that script tags are not executed
    expect(container.querySelector('script')).toBeNull()
    
    // Check that dangerous attributes are removed
    expect(container.querySelector('[onclick]')).toBeNull()
    expect(container.querySelector('[onload]')).toBeNull()
  },
  
  // CSRF protection testing
  testCSRFProtection: async (endpoint: string) => {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ malicious: 'data' })
    })
    
    // Should fail without proper CSRF token
    expect(response.status).toBe(403)
  },
  
  // Input validation testing
  testInputValidation: (validator: Function, testCases: Array<{ input: any, shouldPass: boolean }>) => {
    testCases.forEach(({ input, shouldPass }) => {
      if (shouldPass) {
        expect(() => validator(input)).not.toThrow()
      } else {
        expect(() => validator(input)).toThrow()
      }
    })
  }
}
```

### **Accessibility Testing**
```typescript
import { axe, toHaveNoViolations } from 'jest-axe'

expect.extend(toHaveNoViolations)

// Automated accessibility testing
export const a11yTests = {
  testAccessibility: async (component: Component) => {
    const { container } = render(() => component({}))
    const results = await axe(container)
    expect(results).toHaveNoViolations()
  },
  
  testKeyboardNavigation: (component: Component) => {
    const { container } = render(() => component({}))
    
    // Test tab navigation
    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    )
    
    expect(focusableElements.length).toBeGreaterThan(0)
    
    // Test that all interactive elements are focusable
    focusableElements.forEach(element => {
      expect(element.getAttribute('tabindex')).not.toBe('-1')
    })
  },
  
  testScreenReaderSupport: (component: Component) => {
    const { container } = render(() => component({}))
    
    // Check for proper ARIA labels
    const interactiveElements = container.querySelectorAll('button, input, select')
    interactiveElements.forEach(element => {
      const hasLabel = element.getAttribute('aria-label') || 
                      element.getAttribute('aria-labelledby') ||
                      container.querySelector(`label[for="${element.id}"]`)
      
      expect(hasLabel).toBeTruthy()
    })
  }
}
```

---

## 📊 **SECURITY MONITORING**

### **Content Security Policy**
```typescript
// CSP violation reporting
export const setupCSPReporting = () => {
  document.addEventListener('securitypolicyviolation', (event) => {
    console.error('CSP Violation:', {
      blockedURI: event.blockedURI,
      violatedDirective: event.violatedDirective,
      originalPolicy: event.originalPolicy,
      sourceFile: event.sourceFile,
      lineNumber: event.lineNumber
    })
    
    // Report to security monitoring service
    fetch('/api/security/csp-violation', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      })
    })
  })
}
```

### **Error Boundary Security**
```typescript
// Secure error boundary
export const SecureErrorBoundary: Component<{ children: JSX.Element }> = (props) => {
  const [error, setError] = createSignal<Error | null>(null)
  
  const handleError = (error: Error) => {
    // Log error securely (don't expose sensitive data)
    console.error('Application error:', {
      message: error.message,
      stack: process.env.NODE_ENV === 'development' ? error.stack : undefined,
      timestamp: new Date().toISOString()
    })
    
    // Report to monitoring service
    fetch('/api/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: error.message,
        userAgent: navigator.userAgent,
        url: window.location.href,
        timestamp: new Date().toISOString()
      })
    })
    
    setError(error)
  }
  
  return (
    <ErrorBoundary fallback={(err) => {
      handleError(err)
      return (
        <div class="error-boundary">
          <h2>Something went wrong</h2>
          <p>We're sorry, but something unexpected happened. Please try refreshing the page.</p>
          <button onClick={() => window.location.reload()}>
            Refresh Page
          </button>
        </div>
      )
    }}>
      {props.children}
    </ErrorBoundary>
  )
}
```

---

*"Security and accessibility are not features to be added later - they are fundamental principles that must be woven into the very fabric of our cosmic interface from the beginning."*

**Next:** [Performance Guidelines](./PERFORMANCE_GUIDELINES.md) | [Testing Guidelines](./TESTING_GUIDELINES.md)
