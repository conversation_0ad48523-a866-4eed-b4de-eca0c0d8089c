# 🎨 DESIGN SYSTEM GUIDELINES
## *The Cosmic Design Language of Divine Proportions*

---

## 🌟 **DESIGN PHILOSOPHY**

### **The Golden Ratio Foundation**
Our entire design system is built upon the Golden Ratio (φ = 1.618), the divine proportion found throughout nature and classical architecture.

```
φ = (1 + √5) / 2 ≈ 1.************...

"The Golden Ratio is the mathematical expression of beauty, 
harmony, and cosmic order in our interface design."
```

### **Sacred Geometry Principles**
- **Fibonacci Sequences** - Natural progression in spacing and sizing
- **Golden Rectangles** - Perfect proportions for layouts
- **Spiral Compositions** - Dynamic visual flow
- **Harmonic Intervals** - Rhythmic spacing patterns

---

## 🎯 **COLOR SYSTEM**

### **Cosmic Color Palette**
Our colors are mathematically derived and energetically balanced:

```css
:root {
  /* Primary Cosmic Colors - Representing Infinity */
  --cosmic-50: #f0f9ff;   /* Lightest cosmic mist */
  --cosmic-100: #e0f2fe;  /* Cosmic clouds */
  --cosmic-200: #bae6fd;  /* Stellar dust */
  --cosmic-300: #7dd3fc;  /* Nebula glow */
  --cosmic-400: #38bdf8;  /* Star light */
  --cosmic-500: #0ea5e9;  /* Core cosmic energy */
  --cosmic-600: #0284c7;  /* Deep space */
  --cosmic-700: #0369a1;  /* Cosmic depths */
  --cosmic-800: #075985;  /* Void darkness */
  --cosmic-900: #0c4a6e;  /* Absolute cosmic */
  --cosmic-950: #082f49;  /* Event horizon */

  /* Golden Colors - Representing Wisdom */
  --golden-50: #fffbeb;   /* Golden dawn */
  --golden-100: #fef3c7;  /* Sunlight */
  --golden-200: #fde68a;  /* Golden rays */
  --golden-300: #fcd34d;  /* Amber glow */
  --golden-400: #fbbf24;  /* Pure gold */
  --golden-500: #f59e0b;  /* Golden core */
  --golden-600: #d97706;  /* Deep amber */
  --golden-700: #b45309;  /* Ancient gold */
  --golden-800: #92400e;  /* Bronze depths */
  --golden-900: #78350f;  /* Golden shadow */
  --golden-950: #451a03;  /* Golden void */

  /* Divine Colors - Representing Transcendence */
  --divine-50: #fdf4ff;   /* Divine light */
  --divine-100: #fae8ff;  /* Ethereal mist */
  --divine-200: #f5d0fe;  /* Spirit glow */
  --divine-300: #f0abfc;  /* Divine radiance */
  --divine-400: #e879f9;  /* Cosmic magenta */
  --divine-500: #d946ef;  /* Pure divine energy */
  --divine-600: #c026d3;  /* Deep divine */
  --divine-700: #a21caf;  /* Divine mystery */
  --divine-800: #86198f;  /* Sacred purple */
  --divine-900: #701a75;  /* Divine depths */
  --divine-950: #4a044e;  /* Divine void */
}
```

### **Color Usage Guidelines**

#### **Primary Colors**
- **Cosmic (Blue)** - Actions, links, primary buttons
- **Golden (Amber)** - Success, highlights, secondary actions
- **Divine (Magenta)** - Special features, premium content

#### **Semantic Colors**
```css
/* Status Colors */
--success: var(--golden-500);    /* Success states */
--warning: var(--golden-400);    /* Warning states */
--error: var(--divine-500);      /* Error states */
--info: var(--cosmic-500);       /* Information */

/* Neutral Colors */
--white: #ffffff;
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-200: #e5e7eb;
--gray-300: #d1d5db;
--gray-400: #9ca3af;
--gray-500: #6b7280;
--gray-600: #4b5563;
--gray-700: #374151;
--gray-800: #1f2937;
--gray-900: #111827;
--black: #000000;
```

### **Gradient System**
```css
/* Cosmic Gradients */
--gradient-cosmic: linear-gradient(135deg, var(--cosmic-500) 0%, var(--cosmic-600) 100%);
--gradient-cosmic-radial: radial-gradient(circle, var(--cosmic-400) 0%, var(--cosmic-700) 100%);

/* Golden Gradients */
--gradient-golden: linear-gradient(135deg, var(--golden-400) 0%, var(--golden-600) 100%);
--gradient-golden-sunset: linear-gradient(135deg, var(--golden-300) 0%, var(--divine-400) 100%);

/* Divine Gradients */
--gradient-divine: linear-gradient(135deg, var(--divine-500) 0%, var(--divine-700) 100%);
--gradient-divine-cosmic: linear-gradient(135deg, var(--divine-400) 0%, var(--cosmic-500) 100%);

/* Multi-dimensional Gradients */
--gradient-cosmic-trinity: linear-gradient(135deg, 
  var(--cosmic-500) 0%, 
  var(--divine-500) 50%, 
  var(--golden-500) 100%);
```

---

## 📏 **SPACING SYSTEM**

### **Golden Ratio Spacing Scale**
```css
:root {
  /* Golden Ratio Base Scale */
  --space-golden-xs: 0.382rem;    /* 1/φ² ≈ 6.13px */
  --space-golden-sm: 0.618rem;    /* 1/φ ≈ 9.89px */
  --space-golden-base: 1rem;      /* 1 = 16px */
  --space-golden-md: 1.618rem;    /* φ ≈ 25.89px */
  --space-golden-lg: 2.618rem;    /* φ² ≈ 41.89px */
  --space-golden-xl: 4.236rem;    /* φ³ ≈ 67.78px */
  --space-golden-2xl: 6.854rem;   /* φ⁴ ≈ 109.66px */
  --space-golden-3xl: 11.09rem;   /* φ⁵ ≈ 177.44px */
  --space-golden-4xl: 17.944rem;  /* φ⁶ ≈ 287.1px */

  /* Fibonacci Sequence Scale */
  --space-fib-1: 0.25rem;   /* 1 = 4px */
  --space-fib-2: 0.5rem;    /* 2 = 8px */
  --space-fib-3: 0.75rem;   /* 3 = 12px */
  --space-fib-5: 1.25rem;   /* 5 = 20px */
  --space-fib-8: 2rem;      /* 8 = 32px */
  --space-fib-13: 3.25rem;  /* 13 = 52px */
  --space-fib-21: 5.25rem;  /* 21 = 84px */
  --space-fib-34: 8.5rem;   /* 34 = 136px */
  --space-fib-55: 13.75rem; /* 55 = 220px */
  --space-fib-89: 22.25rem; /* 89 = 356px */
  --space-fib-144: 36rem;   /* 144 = 576px */
}
```

### **Spacing Usage Guidelines**

#### **Component Spacing**
```css
/* Internal component spacing */
.component {
  padding: var(--space-golden-sm) var(--space-golden-md);
  gap: var(--space-golden-sm);
}

/* Between related elements */
.related-elements {
  margin-bottom: var(--space-golden-sm);
}

/* Between sections */
.section-spacing {
  margin-bottom: var(--space-golden-lg);
}

/* Page-level spacing */
.page-spacing {
  margin-bottom: var(--space-golden-xl);
}
```

---

## 📝 **TYPOGRAPHY SYSTEM**

### **Font Families**
```css
:root {
  /* Primary font stack */
  --font-primary: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  
  /* Monospace for code */
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
  
  /* Display font for headings */
  --font-display: 'Inter Display', var(--font-primary);
}
```

### **Type Scale (Golden Ratio Based)**
```css
:root {
  /* Font sizes following golden ratio progression */
  --text-xs: 0.618rem;      /* 1/φ ≈ 9.89px */
  --text-sm: 0.875rem;      /* √φ/φ ≈ 14px */
  --text-base: 1rem;        /* 1 = 16px */
  --text-lg: 1.125rem;      /* φ/√φ ≈ 18px */
  --text-xl: 1.25rem;       /* φ^0.5 ≈ 20px */
  --text-2xl: 1.5rem;       /* φ^0.75 ≈ 24px */
  --text-3xl: 1.875rem;     /* φ ≈ 30px */
  --text-4xl: 2.25rem;      /* φ^1.25 ≈ 36px */
  --text-5xl: 3rem;         /* φ^1.5 ≈ 48px */
  --text-6xl: 3.75rem;      /* φ^1.75 ≈ 60px */
  --text-7xl: 4.5rem;       /* φ^2 ≈ 72px */
  --text-8xl: 6rem;         /* φ^2.5 ≈ 96px */
  --text-9xl: 8rem;         /* φ^3 ≈ 128px */

  /* Line heights following golden ratio */
  --leading-tight: 1.236;    /* φ^-0.5 */
  --leading-normal: 1.618;   /* φ */
  --leading-relaxed: 2.618;  /* φ^2 */
}
```

### **Typography Classes**
```css
/* Heading styles */
.heading-1 {
  font-size: var(--text-6xl);
  line-height: var(--leading-tight);
  font-weight: 800;
  letter-spacing: -0.025em;
}

.heading-2 {
  font-size: var(--text-4xl);
  line-height: var(--leading-tight);
  font-weight: 700;
  letter-spacing: -0.025em;
}

.heading-3 {
  font-size: var(--text-2xl);
  line-height: var(--leading-normal);
  font-weight: 600;
}

/* Body text styles */
.body-large {
  font-size: var(--text-lg);
  line-height: var(--leading-relaxed);
}

.body-base {
  font-size: var(--text-base);
  line-height: var(--leading-normal);
}

.body-small {
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
}

/* Special text styles */
.text-cosmic {
  background: var(--gradient-cosmic);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-golden {
  background: var(--gradient-golden);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-divine {
  background: var(--gradient-divine);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
```

---

## 🎭 **ANIMATION SYSTEM**

### **Timing Functions**
```css
:root {
  /* Golden ratio based durations */
  --duration-instant: 0.1s;
  --duration-fast: 0.236s;      /* φ^-2 */
  --duration-normal: 0.382s;    /* φ^-1 */
  --duration-slow: 0.618s;      /* 1/φ */
  --duration-slower: 1s;        /* 1 */
  --duration-slowest: 1.618s;   /* φ */

  /* Physics-based easing curves */
  --easing-linear: linear;
  --easing-ease: ease;
  --easing-ease-in: ease-in;
  --easing-ease-out: ease-out;
  --easing-ease-in-out: ease-in-out;
  
  /* Custom cosmic easings */
  --easing-golden: cubic-bezier(0.618, 0, 0.382, 1);
  --easing-cosmic: cubic-bezier(0.25, 0.46, 0.45, 0.94);
  --easing-divine: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --easing-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);
}
```

### **Animation Utilities**
```css
/* Transition utilities */
.transition-all {
  transition: all var(--duration-normal) var(--easing-golden);
}

.transition-colors {
  transition: color var(--duration-fast) var(--easing-ease-out),
              background-color var(--duration-fast) var(--easing-ease-out),
              border-color var(--duration-fast) var(--easing-ease-out);
}

.transition-transform {
  transition: transform var(--duration-normal) var(--easing-cosmic);
}

/* Hover effects */
.hover-lift {
  transition: transform var(--duration-normal) var(--easing-cosmic);
}

.hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--easing-golden);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(14, 165, 233, 0.4);
}
```

### **Keyframe Animations**
```css
/* Golden ratio pulse */
@keyframes golden-pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.618; }
}

/* Cosmic float */
@keyframes cosmic-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Divine glow */
@keyframes divine-glow {
  0% { box-shadow: 0 0 5px var(--divine-500); }
  50% { box-shadow: 0 0 20px var(--divine-500), 0 0 30px var(--divine-400); }
  100% { box-shadow: 0 0 5px var(--divine-500); }
}

/* Golden spiral */
@keyframes golden-spiral {
  0% { transform: rotate(0deg) scale(1); }
  100% { transform: rotate(360deg) scale(1.618); }
}
```

---

## 📐 **LAYOUT SYSTEM**

### **Grid System**
```css
/* Golden ratio grid */
.grid-golden {
  display: grid;
  gap: var(--space-golden-md);
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* Fibonacci grid */
.grid-fibonacci {
  display: grid;
  gap: var(--space-fib-8);
  grid-template-columns: 
    var(--space-fib-21) 
    var(--space-fib-34) 
    var(--space-fib-55);
}

/* Sacred geometry layouts */
.layout-golden-rectangle {
  aspect-ratio: 1.618;
}

.layout-golden-square {
  aspect-ratio: 1;
}
```

### **Container System**
```css
/* Container sizes following golden ratio */
.container-xs { max-width: 20rem; }      /* 320px */
.container-sm { max-width: 32rem; }      /* 512px */
.container-md { max-width: 51.8rem; }    /* 829px ≈ 512 * φ */
.container-lg { max-width: 83.8rem; }    /* 1341px ≈ 829 * φ */
.container-xl { max-width: 135.6rem; }   /* 2170px ≈ 1341 * φ */
```

---

## 🎨 **COMPONENT TOKENS**

### **Button Tokens**
```css
.button {
  /* Base properties */
  --button-font-family: var(--font-primary);
  --button-font-weight: 500;
  --button-border-radius: 0.5rem;
  --button-transition: all var(--duration-normal) var(--easing-golden);
  
  /* Size variants */
  --button-sm-padding: var(--space-golden-xs) var(--space-golden-sm);
  --button-sm-font-size: var(--text-sm);
  
  --button-md-padding: var(--space-golden-sm) var(--space-golden-md);
  --button-md-font-size: var(--text-base);
  
  --button-lg-padding: var(--space-golden-md) var(--space-golden-lg);
  --button-lg-font-size: var(--text-lg);
}
```

### **Card Tokens**
```css
.card {
  /* Base properties */
  --card-background: rgba(255, 255, 255, 0.1);
  --card-border: 1px solid rgba(255, 255, 255, 0.2);
  --card-border-radius: 1rem;
  --card-backdrop-filter: blur(10px);
  --card-padding: var(--space-golden-md);
  --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --card-transition: all var(--duration-normal) var(--easing-golden);
}
```

---

*"Design is not just what it looks like and feels like. Design is how it works. In our cosmic design system, every pixel serves a purpose, every color carries meaning, and every animation tells a story."*

**Next:** [Animation Guidelines](./ANIMATION_GUIDELINES.md) | [Accessibility Guidelines](./ACCESSIBILITY_GUIDELINES.md)
