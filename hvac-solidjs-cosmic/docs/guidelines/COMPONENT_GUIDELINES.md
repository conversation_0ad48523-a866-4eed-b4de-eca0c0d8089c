# 🎭 COMPONENT GUIDELINES
## *Building Cosmic-Level Components with SolidJS*

---

## 🏗️ **COMPONENT ARCHITECTURE**

### **Atomic Design Hierarchy**

```
Pages (Level 8) ←─────────── Complete user interfaces
    ↑
Templates (Level 5) ←─────── Page layouts & structures  
    ↑
Organisms (Level 3) ←─────── Complex UI sections
    ↑
Molecules (Level 2) ←─────── Simple component groups
    ↑
Atoms (Level 1) ←─────────── Basic building blocks
```

### **Component Responsibility Matrix**

| Level | Responsibility | State | Business Logic | Styling |
|-------|---------------|-------|----------------|---------|
| **Atoms** | Single UI element | Local only | None | Full |
| **Molecules** | Simple patterns | Local + props | Minimal | Composition |
| **Organisms** | Complex sections | Local + global | Yes | Layout |
| **Templates** | Page structure | Props only | None | Grid/Layout |
| **Pages** | Complete flows | Global | Full | Minimal |

---

## ⚛️ **ATOM COMPONENTS**

### **Structure Template**
```typescript
import { type Component, type JSX, splitProps } from 'solid-js'

export interface AtomProps extends JSX.HTMLAttributes<HTMLElement> {
  variant?: 'primary' | 'secondary' | 'tertiary'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  children: JSX.Element
}

export const AtomComponent: Component<AtomProps> = (props) => {
  const [local, others] = splitProps(props, [
    'variant', 
    'size', 
    'disabled', 
    'children',
    'class'
  ])

  const getVariantClasses = () => {
    switch (local.variant) {
      case 'primary': return 'bg-cosmic-500 text-white'
      case 'secondary': return 'bg-golden-500 text-white'
      case 'tertiary': return 'bg-divine-500 text-white'
      default: return 'bg-cosmic-500 text-white'
    }
  }

  const getSizeClasses = () => {
    switch (local.size) {
      case 'sm': return 'px-golden-sm py-golden-xs text-sm'
      case 'md': return 'px-golden-md py-golden-sm text-base'
      case 'lg': return 'px-golden-lg py-golden-md text-lg'
      case 'xl': return 'px-golden-xl py-golden-lg text-xl'
      default: return 'px-golden-md py-golden-sm text-base'
    }
  }

  const baseClasses = `
    relative overflow-hidden rounded-lg font-medium
    transition-all duration-300 ease-out
    focus:outline-none focus:ring-2 focus:ring-white/50
    disabled:opacity-50 disabled:cursor-not-allowed
    ${getVariantClasses()}
    ${getSizeClasses()}
    ${local.class || ''}
  `.trim().replace(/\s+/g, ' ')

  return (
    <button 
      class={baseClasses} 
      disabled={local.disabled}
      {...others}
    >
      {local.children}
    </button>
  )
}
```

### **Atom Best Practices**
- ✅ **Single responsibility** - One clear purpose
- ✅ **No business logic** - Pure presentation
- ✅ **Flexible styling** - Accept custom classes
- ✅ **Accessible by default** - ARIA attributes
- ✅ **TypeScript strict** - Full type coverage

### **Atom Examples**
```typescript
// ✅ Good atoms
<GoldenButton variant="cosmic" size="lg">Click Me</GoldenButton>
<CosmicInput placeholder="Enter text" />
<DivineIcon name="star" size="md" />
<SacredLabel required>Name</SacredLabel>

// ❌ Bad atoms (too complex)
<CustomerFormButton onSubmit={handleSubmit} customer={customer} />
```

---

## 🧬 **MOLECULE COMPONENTS**

### **Structure Template**
```typescript
import { type Component, type JSX, createSignal } from 'solid-js'
import { GoldenButton } from '../atoms/GoldenButton'
import { CosmicInput } from '../atoms/CosmicInput'

export interface MoleculeProps {
  label: string
  placeholder?: string
  required?: boolean
  error?: string
  onSubmit?: (value: string) => void
  class?: string
}

export const SearchMolecule: Component<MoleculeProps> = (props) => {
  const [value, setValue] = createSignal('')

  const handleSubmit = (e: Event) => {
    e.preventDefault()
    props.onSubmit?.(value())
  }

  return (
    <form 
      onSubmit={handleSubmit}
      class={`flex gap-golden-sm ${props.class || ''}`}
    >
      <div class="flex-1">
        <CosmicInput
          label={props.label}
          placeholder={props.placeholder}
          required={props.required}
          error={props.error}
          value={value()}
          onInput={(e) => setValue(e.currentTarget.value)}
        />
      </div>
      <GoldenButton 
        type="submit" 
        variant="cosmic"
        disabled={!value().trim()}
      >
        Search
      </GoldenButton>
    </form>
  )
}
```

### **Molecule Best Practices**
- ✅ **Compose atoms** - Build from smaller components
- ✅ **Simple state** - Local state only
- ✅ **Clear interface** - Well-defined props
- ✅ **Reusable patterns** - Common UI patterns
- ✅ **Event handling** - Delegate to parent

---

## 🦠 **ORGANISM COMPONENTS**

### **Structure Template**
```typescript
import { type Component, createSignal, createEffect } from 'solid-js'
import { createStore } from 'solid-js/store'

export interface OrganismProps {
  data?: any[]
  loading?: boolean
  onAction?: (action: string, data: any) => void
  class?: string
}

export const CustomerListOrganism: Component<OrganismProps> = (props) => {
  const [filters, setFilters] = createStore({
    search: '',
    status: 'all',
    sortBy: 'name'
  })
  
  const [localData, setLocalData] = createSignal(props.data || [])

  // Sync with external data
  createEffect(() => {
    if (props.data) {
      setLocalData(props.data)
    }
  })

  // Filter and sort data
  const filteredData = createMemo(() => {
    return localData()
      .filter(item => 
        item.name.toLowerCase().includes(filters.search.toLowerCase())
      )
      .filter(item => 
        filters.status === 'all' || item.status === filters.status
      )
      .sort((a, b) => a[filters.sortBy].localeCompare(b[filters.sortBy]))
  })

  return (
    <div class={`cosmic-organism ${props.class || ''}`}>
      {/* Header with controls */}
      <div class="organism-header">
        <SearchMolecule
          label="Search customers"
          placeholder="Type to search..."
          onSubmit={(value) => setFilters('search', value)}
        />
        <FilterMolecule
          options={statusOptions}
          value={filters.status}
          onChange={(value) => setFilters('status', value)}
        />
      </div>

      {/* Content */}
      <div class="organism-content">
        <Show 
          when={!props.loading} 
          fallback={<LoadingSpinner />}
        >
          <For each={filteredData()}>
            {(item) => (
              <CustomerCard
                customer={item}
                onEdit={() => props.onAction?.('edit', item)}
                onDelete={() => props.onAction?.('delete', item)}
              />
            )}
          </For>
        </Show>
      </div>
    </div>
  )
}
```

### **Organism Best Practices**
- ✅ **Business logic** - Domain-specific functionality
- ✅ **State management** - Complex local state
- ✅ **Data integration** - API calls and caching
- ✅ **Event coordination** - Handle multiple interactions
- ✅ **Error boundaries** - Graceful error handling

---

## 📄 **TEMPLATE COMPONENTS**

### **Structure Template**
```typescript
import { type Component, type JSX } from 'solid-js'

export interface TemplateProps {
  header?: JSX.Element
  sidebar?: JSX.Element
  main: JSX.Element
  footer?: JSX.Element
  class?: string
}

export const DashboardTemplate: Component<TemplateProps> = (props) => {
  return (
    <div class={`dashboard-template ${props.class || ''}`}>
      {/* Golden Ratio Grid Layout */}
      <div class="template-grid">
        {/* Header - φ⁻² height */}
        <Show when={props.header}>
          <header class="template-header">
            {props.header}
          </header>
        </Show>

        <div class="template-body">
          {/* Sidebar - φ⁻¹ width */}
          <Show when={props.sidebar}>
            <aside class="template-sidebar">
              {props.sidebar}
            </aside>
          </Show>

          {/* Main - φ width */}
          <main class="template-main">
            {props.main}
          </main>
        </div>

        {/* Footer - φ⁻³ height */}
        <Show when={props.footer}>
          <footer class="template-footer">
            {props.footer}
          </footer>
        </Show>
      </div>
    </div>
  )
}
```

### **Template CSS (Golden Ratio Grid)**
```css
.template-grid {
  display: grid;
  grid-template-rows: auto 1fr auto;
  grid-template-columns: auto 1fr;
  min-height: 100vh;
  
  /* Golden Ratio proportions */
  --header-height: calc(100vh / (var(--golden-ratio) * var(--golden-ratio)));
  --sidebar-width: calc(100vw / var(--golden-ratio));
  --footer-height: calc(100vh / (var(--golden-ratio) * var(--golden-ratio) * var(--golden-ratio)));
}

.template-header {
  grid-column: 1 / -1;
  height: var(--header-height);
}

.template-sidebar {
  width: var(--sidebar-width);
  min-width: 250px;
  max-width: 320px;
}

.template-main {
  flex: 1;
  padding: var(--space-golden-md);
}

.template-footer {
  grid-column: 1 / -1;
  height: var(--footer-height);
}
```

---

## 📱 **PAGE COMPONENTS**

### **Structure Template**
```typescript
import { type Component, createResource, createSignal } from 'solid-js'
import { useNavigate, useParams } from '@solidjs/router'

export const CustomerPage: Component = () => {
  const params = useParams()
  const navigate = useNavigate()
  
  // Data fetching
  const [customer] = createResource(
    () => params.id,
    fetchCustomer
  )
  
  // Page state
  const [isEditing, setIsEditing] = createSignal(false)
  
  // Actions
  const handleEdit = () => setIsEditing(true)
  const handleSave = async (data: CustomerData) => {
    await saveCustomer(params.id, data)
    setIsEditing(false)
  }
  const handleDelete = async () => {
    await deleteCustomer(params.id)
    navigate('/customers')
  }

  return (
    <DashboardTemplate
      header={
        <PageHeader
          title="Customer Details"
          breadcrumbs={[
            { label: 'Dashboard', href: '/' },
            { label: 'Customers', href: '/customers' },
            { label: customer()?.name || 'Loading...', current: true }
          ]}
        />
      }
      main={
        <Show 
          when={customer()} 
          fallback={<PageSkeleton />}
        >
          <CustomerDetailsOrganism
            customer={customer()!}
            editing={isEditing()}
            onEdit={handleEdit}
            onSave={handleSave}
            onDelete={handleDelete}
            onCancel={() => setIsEditing(false)}
          />
        </Show>
      }
    />
  )
}
```

---

## 🎨 **STYLING GUIDELINES**

### **CSS Class Naming**
```css
/* Component classes */
.cosmic-button { /* Base component */ }
.cosmic-button--primary { /* Variant */ }
.cosmic-button--large { /* Size modifier */ }
.cosmic-button__icon { /* Element */ }
.cosmic-button--loading { /* State */ }

/* Utility classes */
.golden-spacing-md { /* Golden ratio spacing */ }
.cosmic-glow { /* Effect utility */ }
.divine-gradient { /* Color utility */ }
```

### **CSS Custom Properties**
```css
.cosmic-component {
  /* Component-specific variables */
  --component-bg: var(--cosmic-500);
  --component-text: var(--white);
  --component-border: var(--cosmic-300);
  --component-shadow: 0 4px 6px rgba(14, 165, 233, 0.1);
  
  /* Golden ratio spacing */
  --component-padding-x: var(--space-golden-md);
  --component-padding-y: var(--space-golden-sm);
  --component-gap: var(--space-golden-sm);
}
```

---

## 🧪 **TESTING COMPONENTS**

### **Component Test Template**
```typescript
import { render, screen, fireEvent } from '@solidjs/testing-library'
import { describe, it, expect, vi } from 'vitest'
import { GoldenButton } from './GoldenButton'

describe('GoldenButton', () => {
  it('renders with correct text', () => {
    render(() => <GoldenButton>Click me</GoldenButton>)
    expect(screen.getByRole('button')).toHaveTextContent('Click me')
  })

  it('applies variant classes correctly', () => {
    render(() => <GoldenButton variant="cosmic">Test</GoldenButton>)
    expect(screen.getByRole('button')).toHaveClass('from-cosmic-500')
  })

  it('handles click events', () => {
    const handleClick = vi.fn()
    render(() => <GoldenButton onClick={handleClick}>Click</GoldenButton>)
    
    fireEvent.click(screen.getByRole('button'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('is accessible', () => {
    render(() => <GoldenButton disabled>Disabled</GoldenButton>)
    expect(screen.getByRole('button')).toBeDisabled()
  })
})
```

---

## 📚 **COMPONENT DOCUMENTATION**

### **JSDoc Template**
```typescript
/**
 * A cosmic button component with golden ratio proportions and physics-based animations.
 * 
 * @example
 * ```tsx
 * <GoldenButton 
 *   variant="cosmic" 
 *   size="lg" 
 *   glow 
 *   physics
 *   onClick={() => console.log('Clicked!')}
 * >
 *   Save Changes
 * </GoldenButton>
 * ```
 * 
 * @param variant - Visual style variant
 * @param size - Size following golden ratio scale
 * @param glow - Enable cosmic glow effect
 * @param physics - Enable physics-based animations
 * @param disabled - Disable button interaction
 * @param children - Button content
 */
export const GoldenButton: Component<GoldenButtonProps> = (props) => {
  // Implementation...
}
```

---

*"Components are the atoms of our cosmic interface. Each one should be crafted with the precision of a master jeweler and the vision of a cosmic architect."*

**Next:** [Design System Guidelines](./DESIGN_SYSTEM.md) | [API Guidelines](./API_GUIDELINES.md)
