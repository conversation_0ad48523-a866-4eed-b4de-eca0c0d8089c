# 🚀 DEPLOYMENT GUIDELINES
## *Launching Cosmic Interfaces to Production*

---

## 🌟 **DEPLOYMENT PHILOSOPHY**

### **Cosmic Deployment Principles**
```
"Deployment is not the end of development - it's the beginning 
of the user experience. Every deployment should be smooth, 
reliable, and reversible."
```

### **Core Deployment Values**
1. **Zero Downtime** - Users should never experience interruptions
2. **Rollback Ready** - Always have a way back
3. **Progressive Delivery** - Gradual rollouts minimize risk
4. **Monitoring First** - Observe before, during, and after
5. **Automation** - Reduce human error through automation

---

## 🏗️ **BUILD PROCESS**

### **Production Build Configuration**
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import solid from 'vite-plugin-solid'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    solid(),
    visualizer({
      filename: 'dist/stats.html',
      open: false,
      gzipSize: true,
      brotliSize: true
    })
  ],
  build: {
    target: 'esnext',
    minify: 'terser',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['solid-js'],
          ui: ['@kobalte/core'],
          utils: ['date-fns', 'zod'],
          animations: ['gsap', 'three']
        }
      }
    },
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  define: {
    __BUILD_TIME__: JSON.stringify(new Date().toISOString()),
    __VERSION__: JSON.stringify(process.env.npm_package_version)
  }
})
```

### **Environment Configuration**
```typescript
// config/environment.ts
export const config = {
  development: {
    API_BASE_URL: 'http://localhost:3000/api',
    ENABLE_DEVTOOLS: true,
    LOG_LEVEL: 'debug'
  },
  staging: {
    API_BASE_URL: 'https://staging-api.hvac-cosmic.com/api',
    ENABLE_DEVTOOLS: true,
    LOG_LEVEL: 'info'
  },
  production: {
    API_BASE_URL: 'https://api.hvac-cosmic.com/api',
    ENABLE_DEVTOOLS: false,
    LOG_LEVEL: 'error'
  }
}

export const getConfig = () => {
  const env = import.meta.env.MODE as keyof typeof config
  return config[env] || config.development
}
```

### **Build Scripts**
```json
{
  "scripts": {
    "build": "vite build",
    "build:staging": "NODE_ENV=staging vite build",
    "build:production": "NODE_ENV=production vite build",
    "build:analyze": "vite build --mode analyze",
    "preview": "vite preview",
    "preview:production": "vite preview --mode production"
  }
}
```

---

## 🐳 **CONTAINERIZATION**

### **Multi-Stage Dockerfile**
```dockerfile
# Build stage
FROM node:18-alpine AS builder

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY pnpm-lock.yaml ./

# Install dependencies
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# Copy source code
COPY . .

# Build application
RUN pnpm run build

# Production stage
FROM nginx:alpine AS production

# Copy built assets
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Add health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

### **Nginx Configuration**
```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Brotli compression (if available)
    brotli on;
    brotli_comp_level 6;
    brotli_types
        text/plain
        text/css
        application/json
        application/javascript
        text/xml
        application/xml
        application/xml+rss
        text/javascript;
    
    server {
        listen 80;
        server_name _;
        root /usr/share/nginx/html;
        index index.html;
        
        # Security headers
        add_header X-Frame-Options "SAMEORIGIN" always;
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:;" always;
        
        # Cache static assets
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Handle SPA routing
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
    }
}
```

### **Docker Compose for Development**
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build:
      context: .
      target: production
    ports:
      - "3000:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    restart: unless-stopped
    
  # Optional: Add monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana-storage:/var/lib/grafana

volumes:
  grafana-storage:
```

---

## ☁️ **CLOUD DEPLOYMENT**

### **Vercel Deployment**
```json
// vercel.json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/static-build",
      "config": {
        "distDir": "dist"
      }
    }
  ],
  "routes": [
    {
      "src": "/assets/(.*)",
      "headers": {
        "cache-control": "public, max-age=31536000, immutable"
      }
    },
    {
      "src": "/(.*)",
      "dest": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### **Netlify Deployment**
```toml
# netlify.toml
[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### **AWS S3 + CloudFront**
```yaml
# aws-deploy.yml (GitHub Actions)
name: Deploy to AWS

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build
        
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1
          
      - name: Deploy to S3
        run: |
          aws s3 sync dist/ s3://${{ secrets.S3_BUCKET }} --delete
          
      - name: Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"
```

---

## 🔄 **CI/CD PIPELINE**

### **GitHub Actions Workflow**
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '18'

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run linting
        run: npm run lint
        
      - name: Run type checking
        run: npm run type-check
        
      - name: Run unit tests
        run: npm run test:unit
        
      - name: Run integration tests
        run: npm run test:integration
        
      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          
  e2e:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Install Playwright
        run: npx playwright install --with-deps
        
      - name: Build application
        run: npm run build
        
      - name: Run E2E tests
        run: npm run test:e2e
        
      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          
  security:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Run security audit
        run: npm audit --audit-level high
        
      - name: Run dependency check
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: 'security-report.sarif'
          
  build:
    runs-on: ubuntu-latest
    needs: [test, e2e, security]
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build application
        run: npm run build:production
        
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/
          
  deploy-staging:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
      - name: Deploy to staging
        run: echo "Deploying to staging environment"
        
  deploy-production:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Deploy to production
        run: echo "Deploying to production environment"
```

---

## 📊 **MONITORING & OBSERVABILITY**

### **Application Monitoring**
```typescript
// monitoring/setup.ts
import { setupWebVitals } from './web-vitals'
import { setupErrorTracking } from './error-tracking'
import { setupPerformanceMonitoring } from './performance'

export const setupMonitoring = () => {
  // Web Vitals tracking
  setupWebVitals()
  
  // Error tracking
  setupErrorTracking()
  
  // Performance monitoring
  setupPerformanceMonitoring()
  
  // Custom metrics
  setupCustomMetrics()
}

const setupCustomMetrics = () => {
  // Track user interactions
  document.addEventListener('click', (event) => {
    const target = event.target as HTMLElement
    if (target.tagName === 'BUTTON') {
      gtag('event', 'button_click', {
        button_text: target.textContent,
        page_path: window.location.pathname
      })
    }
  })
  
  // Track page views
  const observer = new PerformanceObserver((list) => {
    list.getEntries().forEach((entry) => {
      if (entry.entryType === 'navigation') {
        gtag('event', 'page_view', {
          page_title: document.title,
          page_location: window.location.href,
          load_time: entry.loadEventEnd - entry.loadEventStart
        })
      }
    })
  })
  
  observer.observe({ entryTypes: ['navigation'] })
}
```

### **Health Checks**
```typescript
// health/checks.ts
export const healthChecks = {
  // Basic health check
  basic: () => ({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: __VERSION__,
    buildTime: __BUILD_TIME__
  }),
  
  // Detailed health check
  detailed: async () => {
    const checks = await Promise.allSettled([
      checkAPI(),
      checkLocalStorage(),
      checkPerformance()
    ])
    
    return {
      status: checks.every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      timestamp: new Date().toISOString(),
      checks: {
        api: checks[0].status === 'fulfilled' ? 'healthy' : 'unhealthy',
        localStorage: checks[1].status === 'fulfilled' ? 'healthy' : 'unhealthy',
        performance: checks[2].status === 'fulfilled' ? 'healthy' : 'unhealthy'
      }
    }
  }
}

const checkAPI = async () => {
  const response = await fetch('/api/health')
  if (!response.ok) throw new Error('API unhealthy')
  return response.json()
}

const checkLocalStorage = () => {
  try {
    localStorage.setItem('test', 'test')
    localStorage.removeItem('test')
    return true
  } catch {
    throw new Error('LocalStorage unavailable')
  }
}

const checkPerformance = () => {
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
  if (navigation.loadEventEnd - navigation.navigationStart > 5000) {
    throw new Error('Page load too slow')
  }
  return true
}
```

---

## 🔄 **ROLLBACK STRATEGIES**

### **Blue-Green Deployment**
```bash
#!/bin/bash
# deploy.sh

set -e

ENVIRONMENT=${1:-staging}
VERSION=${2:-latest}

echo "Deploying version $VERSION to $ENVIRONMENT"

# Build new version
docker build -t hvac-cosmic:$VERSION .

# Deploy to green environment
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d --scale app=2

# Health check
sleep 30
curl -f http://localhost:3000/health || exit 1

# Switch traffic (blue -> green)
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d nginx

# Remove old version (blue)
docker-compose -f docker-compose.$ENVIRONMENT.yml scale app-old=0

echo "Deployment completed successfully"
```

### **Canary Deployment**
```yaml
# k8s/canary-deployment.yml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: hvac-cosmic
spec:
  replicas: 5
  strategy:
    canary:
      steps:
      - setWeight: 20
      - pause: {duration: 10m}
      - setWeight: 40
      - pause: {duration: 10m}
      - setWeight: 60
      - pause: {duration: 10m}
      - setWeight: 80
      - pause: {duration: 10m}
  selector:
    matchLabels:
      app: hvac-cosmic
  template:
    metadata:
      labels:
        app: hvac-cosmic
    spec:
      containers:
      - name: hvac-cosmic
        image: hvac-cosmic:latest
        ports:
        - containerPort: 80
```

---

*"Deployment is the moment when our cosmic creation meets the real world. Every deployment should be a celebration of quality, reliability, and the seamless delivery of exceptional user experiences."*

**Next:** [API Documentation](../api/API_REFERENCE.md) | [Component Library](../components/COMPONENT_LIBRARY.md)
