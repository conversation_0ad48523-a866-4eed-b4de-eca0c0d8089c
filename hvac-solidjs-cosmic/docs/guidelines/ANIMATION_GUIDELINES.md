# ⚡ ANIMATION GUIDELINES
## *Physics-Based Motion Design for Cosmic Interfaces*

---

## 🌊 **ANIMATION PHILOSOPHY**

### **Natural Motion Principles**
Our animations are inspired by the laws of physics and natural phenomena:

```
"Animation is not just decoration - it's the breath of life 
that transforms static interfaces into living, breathing 
cosmic experiences."
```

### **Core Animation Principles**
1. **Easing & Timing** - Follow natural acceleration and deceleration
2. **Anticipation** - Prepare users for what's coming next
3. **Follow Through** - Complete motions feel satisfying
4. **Secondary Action** - Supporting animations enhance the primary action
5. **Staging** - Direct attention to what's important
6. **Appeal** - Make interactions delightful and memorable

---

## ⏱️ **TIMING SYSTEM**

### **Golden Ratio Durations**
```css
:root {
  /* Micro-interactions (0-300ms) */
  --duration-instant: 0.1s;      /* Immediate feedback */
  --duration-fast: 0.236s;       /* φ^-2 - Quick transitions */
  --duration-normal: 0.382s;     /* φ^-1 - Standard transitions */
  
  /* Macro-interactions (300ms-1s) */
  --duration-slow: 0.618s;       /* 1/φ - Deliberate actions */
  --duration-slower: 1s;         /* 1 - Major state changes */
  
  /* Cinematic (1s+) */
  --duration-slowest: 1.618s;    /* φ - Dramatic reveals */
  --duration-epic: 2.618s;       /* φ² - Epic transformations */
}
```

### **Duration Usage Guidelines**

| Duration | Use Case | Examples |
|----------|----------|----------|
| **Instant (0.1s)** | Immediate feedback | Button press, hover states |
| **Fast (0.236s)** | Quick transitions | Color changes, opacity |
| **Normal (0.382s)** | Standard interactions | Modal open/close, dropdown |
| **Slow (0.618s)** | Deliberate actions | Page transitions, form submission |
| **Slower (1s)** | Major changes | Route changes, data loading |
| **Slowest (1.618s)** | Dramatic reveals | Onboarding, feature introductions |

---

## 🎭 **EASING FUNCTIONS**

### **Physics-Based Easing**
```css
:root {
  /* Natural easing curves */
  --easing-linear: linear;
  --easing-ease: ease;
  --easing-ease-in: ease-in;
  --easing-ease-out: ease-out;
  --easing-ease-in-out: ease-in-out;
  
  /* Custom cosmic easings */
  --easing-golden: cubic-bezier(0.618, 0, 0.382, 1);      /* Golden ratio curve */
  --easing-cosmic: cubic-bezier(0.25, 0.46, 0.45, 0.94);  /* Smooth cosmic flow */
  --easing-divine: cubic-bezier(0.68, -0.55, 0.265, 1.55); /* Divine bounce */
  --easing-bounce: cubic-bezier(0.68, -0.6, 0.32, 1.6);   /* Playful bounce */
  --easing-elastic: cubic-bezier(0.68, -0.75, 0.265, 1.75); /* Elastic snap */
}
```

### **Easing Selection Guide**

#### **Entrance Animations**
```css
/* Elements appearing */
.enter-fade {
  animation: fadeIn var(--duration-normal) var(--easing-ease-out);
}

.enter-slide-up {
  animation: slideUp var(--duration-normal) var(--easing-cosmic);
}

.enter-scale {
  animation: scaleIn var(--duration-normal) var(--easing-divine);
}
```

#### **Exit Animations**
```css
/* Elements disappearing */
.exit-fade {
  animation: fadeOut var(--duration-fast) var(--easing-ease-in);
}

.exit-slide-down {
  animation: slideDown var(--duration-fast) var(--easing-ease-in);
}

.exit-scale {
  animation: scaleOut var(--duration-fast) var(--easing-ease-in);
}
```

#### **Interactive Animations**
```css
/* User interactions */
.hover-lift {
  transition: transform var(--duration-fast) var(--easing-cosmic);
}

.hover-lift:hover {
  transform: translateY(-2px) scale(1.02);
}

.click-bounce {
  transition: transform var(--duration-fast) var(--easing-bounce);
}

.click-bounce:active {
  transform: scale(0.95);
}
```

---

## 🎨 **ANIMATION TYPES**

### **1. Micro-Interactions**
Small, delightful animations that provide immediate feedback:

```css
/* Button hover effect */
.button-cosmic {
  position: relative;
  overflow: hidden;
  transition: all var(--duration-fast) var(--easing-golden);
}

.button-cosmic::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width var(--duration-normal) var(--easing-cosmic),
              height var(--duration-normal) var(--easing-cosmic);
}

.button-cosmic:hover::before {
  width: 300px;
  height: 300px;
}

/* Input focus glow */
.input-cosmic {
  border: 2px solid transparent;
  transition: all var(--duration-normal) var(--easing-golden);
}

.input-cosmic:focus {
  border-color: var(--cosmic-500);
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}
```

### **2. State Transitions**
Smooth transitions between different states:

```css
/* Loading state */
@keyframes cosmic-pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.618;
    transform: scale(1.05);
  }
}

.loading-cosmic {
  animation: cosmic-pulse var(--duration-slower) var(--easing-ease-in-out) infinite;
}

/* Success state */
@keyframes success-checkmark {
  0% {
    stroke-dasharray: 0 100;
  }
  100% {
    stroke-dasharray: 100 0;
  }
}

.success-icon {
  animation: success-checkmark var(--duration-slow) var(--easing-cosmic);
}
```

### **3. Layout Animations**
Smooth transitions when layout changes:

```css
/* Grid item reordering */
.grid-item {
  transition: all var(--duration-normal) var(--easing-cosmic);
}

/* Modal entrance */
@keyframes modal-enter {
  0% {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal {
  animation: modal-enter var(--duration-normal) var(--easing-divine);
}

/* Page transition */
@keyframes page-slide-in {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.page-enter {
  animation: page-slide-in var(--duration-slow) var(--easing-cosmic);
}
```

### **4. Ambient Animations**
Subtle, continuous animations that add life:

```css
/* Floating particles */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.particle {
  animation: float 3s var(--easing-ease-in-out) infinite;
}

.particle:nth-child(2) { animation-delay: 0.5s; }
.particle:nth-child(3) { animation-delay: 1s; }
.particle:nth-child(4) { animation-delay: 1.5s; }

/* Cosmic glow */
@keyframes cosmic-glow {
  0%, 100% {
    box-shadow: 0 0 10px var(--cosmic-500);
  }
  50% {
    box-shadow: 0 0 20px var(--cosmic-400), 0 0 30px var(--cosmic-500);
  }
}

.cosmic-glow {
  animation: cosmic-glow 2s var(--easing-ease-in-out) infinite;
}
```

---

## 🎪 **ADVANCED ANIMATIONS**

### **GSAP Integration**
For complex animations, we use GSAP:

```typescript
import { gsap } from 'gsap'

// Golden ratio spring animation
const goldenSpring = {
  duration: 0.618,
  ease: "elastic.out(1, 0.618)",
  stagger: 0.1
}

// Cosmic entrance animation
export const animateCosmicEntrance = (elements: Element[]) => {
  gsap.fromTo(elements, 
    {
      opacity: 0,
      y: 50,
      scale: 0.8,
      rotation: -5
    },
    {
      opacity: 1,
      y: 0,
      scale: 1,
      rotation: 0,
      ...goldenSpring,
      stagger: 0.1
    }
  )
}

// Divine reveal animation
export const animateDivineReveal = (element: Element) => {
  const tl = gsap.timeline()
  
  tl.fromTo(element,
    { scale: 0, rotation: 180 },
    { scale: 1, rotation: 0, duration: 0.618, ease: "back.out(1.7)" }
  )
  .to(element,
    { 
      boxShadow: "0 0 30px rgba(217, 70, 239, 0.5)",
      duration: 0.382,
      ease: "power2.out"
    },
    "-=0.2"
  )
}
```

### **Three.js 3D Animations**
For 3D cosmic effects:

```typescript
import * as THREE from 'three'

// Golden spiral geometry
export const createGoldenSpiral = () => {
  const points = []
  const goldenAngle = Math.PI * (3 - Math.sqrt(5)) // Golden angle in radians
  
  for (let i = 0; i < 137; i++) {
    const angle = i * goldenAngle
    const radius = Math.sqrt(i) * 0.618
    
    points.push(new THREE.Vector3(
      radius * Math.cos(angle),
      radius * Math.sin(angle),
      i * 0.1
    ))
  }
  
  return new THREE.CatmullRomCurve3(points)
}

// Cosmic particle system
export const createCosmicParticles = (scene: THREE.Scene) => {
  const geometry = new THREE.BufferGeometry()
  const positions = new Float32Array(1000 * 3)
  
  for (let i = 0; i < 1000; i++) {
    positions[i * 3] = (Math.random() - 0.5) * 100
    positions[i * 3 + 1] = (Math.random() - 0.5) * 100
    positions[i * 3 + 2] = (Math.random() - 0.5) * 100
  }
  
  geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
  
  const material = new THREE.PointsMaterial({
    color: 0x0ea5e9,
    size: 0.5,
    transparent: true,
    opacity: 0.8
  })
  
  const particles = new THREE.Points(geometry, material)
  scene.add(particles)
  
  return particles
}
```

---

## 🎯 **ANIMATION PERFORMANCE**

### **Performance Guidelines**
1. **Use CSS transforms** instead of changing layout properties
2. **Prefer opacity** over visibility changes
3. **Use will-change** sparingly and remove after animation
4. **Batch DOM reads and writes** to avoid layout thrashing
5. **Use requestAnimationFrame** for JavaScript animations

### **Optimized Animation Properties**
```css
/* ✅ Good - GPU accelerated */
.optimized-animation {
  transform: translateX(100px);
  opacity: 0.5;
  filter: blur(5px);
}

/* ❌ Bad - causes layout/paint */
.slow-animation {
  left: 100px;
  width: 200px;
  background-color: red;
}
```

### **Performance Monitoring**
```typescript
// Monitor animation performance
export const measureAnimationPerformance = (animationName: string, callback: () => void) => {
  const start = performance.now()
  
  callback()
  
  requestAnimationFrame(() => {
    const end = performance.now()
    console.log(`${animationName} took ${end - start} milliseconds`)
  })
}
```

---

## 🎨 **ANIMATION UTILITIES**

### **SolidJS Animation Helpers**
```typescript
import { createSignal, createEffect, onCleanup } from 'solid-js'

// Smooth value interpolation
export const createSmoothValue = (initialValue: number, duration = 300) => {
  const [value, setValue] = createSignal(initialValue)
  const [target, setTarget] = createSignal(initialValue)
  
  createEffect(() => {
    const targetValue = target()
    const currentValue = value()
    
    if (Math.abs(targetValue - currentValue) < 0.01) return
    
    const startTime = performance.now()
    const startValue = currentValue
    const difference = targetValue - startValue
    
    const animate = (currentTime: number) => {
      const elapsed = currentTime - startTime
      const progress = Math.min(elapsed / duration, 1)
      
      // Golden ratio easing
      const eased = 1 - Math.pow(1 - progress, 1.618)
      const newValue = startValue + difference * eased
      
      setValue(newValue)
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    requestAnimationFrame(animate)
  })
  
  return [value, setTarget] as const
}

// Intersection observer for scroll animations
export const createScrollAnimation = (threshold = 0.1) => {
  const [isVisible, setIsVisible] = createSignal(false)
  
  const observe = (element: Element) => {
    const observer = new IntersectionObserver(
      ([entry]) => setIsVisible(entry.isIntersecting),
      { threshold }
    )
    
    observer.observe(element)
    
    onCleanup(() => observer.disconnect())
  }
  
  return [isVisible, observe] as const
}
```

### **Animation Composition**
```typescript
// Combine multiple animations
export const createAnimationSequence = (animations: Array<() => Promise<void>>) => {
  return async () => {
    for (const animation of animations) {
      await animation()
    }
  }
}

// Parallel animations
export const createAnimationParallel = (animations: Array<() => Promise<void>>) => {
  return () => Promise.all(animations.map(anim => anim()))
}
```

---

## 📱 **RESPONSIVE ANIMATIONS**

### **Reduced Motion Support**
```css
/* Respect user preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
```

### **Device-Specific Optimizations**
```css
/* Mobile optimizations */
@media (max-width: 768px) {
  .animation-heavy {
    animation: none;
  }
  
  .transition-all {
    transition-duration: var(--duration-fast);
  }
}

/* High refresh rate displays */
@media (min-resolution: 120dpi) {
  .smooth-animation {
    animation-duration: calc(var(--duration-normal) * 0.8);
  }
}
```

---

*"Animation is the poetry of motion. In our cosmic interface, every movement tells a story, every transition carries meaning, and every interaction creates a moment of magic."*

**Next:** [Performance Guidelines](./PERFORMANCE_GUIDELINES.md) | [Testing Guidelines](./TESTING_GUIDELINES.md)
