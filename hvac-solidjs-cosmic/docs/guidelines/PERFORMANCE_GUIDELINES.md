# 📊 PERFORMANCE GUIDELINES
## *Optimizing Cosmic Interfaces for Lightning-Fast Experiences*

---

## ⚡ **PERFORMANCE PHILOSOPHY**

### **Core Performance Principles**
```
"Performance is not just about speed - it's about creating 
seamless, delightful experiences that feel instantaneous 
and effortless to our users."
```

### **Golden Performance Metrics**
Based on Core Web Vitals and cosmic standards:

| Metric | Target | Cosmic Target | Description |
|--------|--------|---------------|-------------|
| **LCP** | < 2.5s | < 1.618s | Largest Contentful Paint |
| **FID** | < 100ms | < 61.8ms | First Input Delay |
| **CLS** | < 0.1 | < 0.0618 | Cumulative Layout Shift |
| **FCP** | < 1.8s | < 1.236s | First Contentful Paint |
| **TTI** | < 3.8s | < 2.618s | Time to Interactive |

---

## 🚀 **BUNDLE OPTIMIZATION**

### **Code Splitting Strategy**
```typescript
// Route-based code splitting
import { lazy } from 'solid-js'

const CustomerPage = lazy(() => import('./pages/CustomerPage'))
const ServicePage = lazy(() => import('./pages/ServicePage'))
const DashboardPage = lazy(() => import('./pages/DashboardPage'))

// Component-based code splitting
const HeavyChart = lazy(() => import('./components/HeavyChart'))

// Feature-based code splitting
const AdminPanel = lazy(() => import('./features/admin/AdminPanel'))

// Usage with Suspense
export const App = () => {
  return (
    <Router>
      <Routes>
        <Route path="/customers" component={() => (
          <Suspense fallback={<PageSkeleton />}>
            <CustomerPage />
          </Suspense>
        )} />
      </Routes>
    </Router>
  )
}
```

### **Tree Shaking Optimization**
```typescript
// ✅ Good: Import only what you need
import { createSignal, createMemo } from 'solid-js'
import { format } from 'date-fns/format'
import { isValid } from 'date-fns/isValid'

// ❌ Bad: Imports entire library
import * as SolidJS from 'solid-js'
import * as dateFns from 'date-fns'

// ✅ Good: Use ES modules
export const formatDate = (date: Date) => format(date, 'yyyy-MM-dd')

// ❌ Bad: CommonJS modules don't tree-shake well
const utils = require('./utils')
```

### **Bundle Analysis**
```bash
# Analyze bundle size
npm run build -- --analyze

# Vite bundle analyzer
npm install --save-dev rollup-plugin-visualizer
```

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    // ... other plugins
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true
    })
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['solid-js'],
          ui: ['@kobalte/core'],
          utils: ['date-fns', 'zod'],
          animations: ['gsap', 'three']
        }
      }
    }
  }
})
```

---

## 🎯 **RUNTIME OPTIMIZATION**

### **SolidJS Performance Patterns**
```typescript
// ✅ Good: Fine-grained reactivity
const [count, setCount] = createSignal(0)
const [name, setName] = createSignal('')

const doubleCount = createMemo(() => count() * 2)

// ❌ Bad: Coarse-grained reactivity
const [state, setState] = createSignal({ count: 0, name: '' })

// ✅ Good: Efficient list rendering
<For each={items()}>
  {(item, index) => (
    <div data-index={index()}>{item.name}</div>
  )}
</For>

// ❌ Bad: Inefficient list rendering
{items().map((item, index) => (
  <div key={index}>{item.name}</div>
))}

// ✅ Good: Conditional rendering
<Show when={isVisible()} fallback={<Skeleton />}>
  <ExpensiveComponent />
</Show>

// ❌ Bad: Always renders both
{isVisible() ? <ExpensiveComponent /> : <Skeleton />}
```

### **Memoization Strategies**
```typescript
// Expensive computation memoization
const expensiveCalculation = createMemo(() => {
  return heavyComputation(data())
})

// Derived state memoization
const filteredItems = createMemo(() => {
  return items().filter(item => 
    item.name.toLowerCase().includes(searchTerm().toLowerCase())
  )
})

// Component memoization for expensive renders
const MemoizedChart = createMemo(() => {
  return <HeavyChart data={chartData()} />
})

// Custom memoization hook
export const createMemoizedCallback = <T extends any[], R>(
  fn: (...args: T) => R,
  deps: () => any[]
) => {
  const [memoized, setMemoized] = createSignal<R>()
  const [lastDeps, setLastDeps] = createSignal<any[]>([])
  
  return (...args: T): R => {
    const currentDeps = deps()
    
    if (!shallowEqual(currentDeps, lastDeps())) {
      const result = fn(...args)
      setMemoized(() => result)
      setLastDeps(currentDeps)
      return result
    }
    
    return memoized()!
  }
}
```

---

## 🖼️ **ASSET OPTIMIZATION**

### **Image Optimization**
```typescript
// Responsive images with WebP support
export const OptimizedImage: Component<{
  src: string
  alt: string
  width: number
  height: number
  loading?: 'lazy' | 'eager'
}> = (props) => {
  const webpSrc = () => props.src.replace(/\.(jpg|jpeg|png)$/, '.webp')
  
  return (
    <picture>
      <source srcset={webpSrc()} type="image/webp" />
      <img
        src={props.src}
        alt={props.alt}
        width={props.width}
        height={props.height}
        loading={props.loading || 'lazy'}
        decoding="async"
      />
    </picture>
  )
}

// Progressive image loading
export const ProgressiveImage: Component<{
  src: string
  placeholder: string
  alt: string
}> = (props) => {
  const [loaded, setLoaded] = createSignal(false)
  const [error, setError] = createSignal(false)
  
  return (
    <div class="relative overflow-hidden">
      <img
        src={props.placeholder}
        alt=""
        class={`absolute inset-0 transition-opacity duration-300 ${
          loaded() ? 'opacity-0' : 'opacity-100'
        }`}
      />
      <img
        src={props.src}
        alt={props.alt}
        class={`transition-opacity duration-300 ${
          loaded() ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={() => setLoaded(true)}
        onError={() => setError(true)}
        loading="lazy"
      />
      <Show when={error()}>
        <div class="absolute inset-0 flex items-center justify-center bg-gray-100">
          <span class="text-gray-500">Failed to load image</span>
        </div>
      </Show>
    </div>
  )
}
```

### **Font Optimization**
```css
/* Font loading optimization */
@font-face {
  font-family: 'Inter';
  font-style: normal;
  font-weight: 400;
  font-display: swap; /* Ensures text remains visible during font load */
  src: url('/fonts/inter-regular.woff2') format('woff2'),
       url('/fonts/inter-regular.woff') format('woff');
}

/* Preload critical fonts */
/* In HTML head: */
/* <link rel="preload" href="/fonts/inter-regular.woff2" as="font" type="font/woff2" crossorigin> */

/* Font subsetting for reduced file size */
/* Include only necessary character sets */
```

---

## 🔄 **CACHING STRATEGIES**

### **HTTP Caching**
```typescript
// Service Worker for aggressive caching
export const setupServiceWorker = () => {
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('SW registered:', registration)
      })
      .catch(error => {
        console.log('SW registration failed:', error)
      })
  }
}

// Cache-first strategy for static assets
// In sw.js:
const CACHE_NAME = 'cosmic-crm-v1'
const STATIC_ASSETS = [
  '/',
  '/static/css/main.css',
  '/static/js/main.js',
  '/fonts/inter-regular.woff2'
]

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(STATIC_ASSETS))
  )
})

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => response || fetch(event.request))
  )
})
```

### **Memory Caching**
```typescript
// LRU Cache implementation
export class LRUCache<K, V> {
  private cache = new Map<K, V>()
  private maxSize: number
  
  constructor(maxSize: number) {
    this.maxSize = maxSize
  }
  
  get(key: K): V | undefined {
    const value = this.cache.get(key)
    if (value !== undefined) {
      // Move to end (most recently used)
      this.cache.delete(key)
      this.cache.set(key, value)
    }
    return value
  }
  
  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key)
    } else if (this.cache.size >= this.maxSize) {
      // Remove least recently used
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }
    this.cache.set(key, value)
  }
}

// API response caching
const apiCache = new LRUCache<string, any>(100)

export const cachedFetch = async (url: string, options?: RequestInit) => {
  const cacheKey = `${url}:${JSON.stringify(options)}`
  
  // Check cache first
  const cached = apiCache.get(cacheKey)
  if (cached) {
    return cached
  }
  
  // Fetch and cache
  const response = await fetch(url, options)
  const data = await response.json()
  
  apiCache.set(cacheKey, data)
  return data
}
```

---

## 📱 **MOBILE OPTIMIZATION**

### **Touch Performance**
```css
/* Optimize touch interactions */
.touch-target {
  min-height: 44px; /* iOS minimum touch target */
  min-width: 44px;
  touch-action: manipulation; /* Disable double-tap zoom */
}

/* Reduce paint on scroll */
.scroll-container {
  -webkit-overflow-scrolling: touch;
  will-change: scroll-position;
}

/* Optimize animations for mobile */
@media (max-width: 768px) {
  .animation-heavy {
    animation: none;
  }
  
  .transition-all {
    transition-duration: 0.2s;
  }
}
```

### **Viewport Optimization**
```html
<!-- Optimal viewport meta tag -->
<meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover">

<!-- Prevent zoom on input focus (iOS) -->
<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
```

---

## 🔍 **PERFORMANCE MONITORING**

### **Core Web Vitals Tracking**
```typescript
// Performance monitoring
export const trackWebVitals = () => {
  // LCP - Largest Contentful Paint
  new PerformanceObserver((list) => {
    const entries = list.getEntries()
    const lastEntry = entries[entries.length - 1]
    
    console.log('LCP:', lastEntry.startTime)
    
    // Send to analytics
    gtag('event', 'web_vitals', {
      name: 'LCP',
      value: Math.round(lastEntry.startTime),
      event_category: 'Performance'
    })
  }).observe({ entryTypes: ['largest-contentful-paint'] })
  
  // FID - First Input Delay
  new PerformanceObserver((list) => {
    const entries = list.getEntries()
    entries.forEach(entry => {
      console.log('FID:', entry.processingStart - entry.startTime)
      
      gtag('event', 'web_vitals', {
        name: 'FID',
        value: Math.round(entry.processingStart - entry.startTime),
        event_category: 'Performance'
      })
    })
  }).observe({ entryTypes: ['first-input'] })
  
  // CLS - Cumulative Layout Shift
  let clsValue = 0
  new PerformanceObserver((list) => {
    const entries = list.getEntries()
    entries.forEach(entry => {
      if (!entry.hadRecentInput) {
        clsValue += entry.value
      }
    })
    
    console.log('CLS:', clsValue)
    
    gtag('event', 'web_vitals', {
      name: 'CLS',
      value: Math.round(clsValue * 1000),
      event_category: 'Performance'
    })
  }).observe({ entryTypes: ['layout-shift'] })
}

// Custom performance marks
export const performanceMark = (name: string) => {
  performance.mark(name)
}

export const performanceMeasure = (name: string, startMark: string, endMark?: string) => {
  performance.measure(name, startMark, endMark)
  
  const measure = performance.getEntriesByName(name, 'measure')[0]
  console.log(`${name}: ${measure.duration}ms`)
  
  return measure.duration
}
```

### **Bundle Size Monitoring**
```typescript
// Bundle size tracking
export const trackBundleSize = () => {
  const scripts = document.querySelectorAll('script[src]')
  const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')
  
  let totalSize = 0
  
  const checkResource = async (url: string, type: string) => {
    try {
      const response = await fetch(url, { method: 'HEAD' })
      const size = parseInt(response.headers.get('content-length') || '0')
      totalSize += size
      
      console.log(`${type} ${url}: ${(size / 1024).toFixed(2)}KB`)
    } catch (error) {
      console.warn(`Failed to get size for ${url}`)
    }
  }
  
  scripts.forEach(script => {
    if (script.src) checkResource(script.src, 'JS')
  })
  
  stylesheets.forEach(link => {
    if (link.href) checkResource(link.href, 'CSS')
  })
  
  setTimeout(() => {
    console.log(`Total bundle size: ${(totalSize / 1024).toFixed(2)}KB`)
    
    gtag('event', 'bundle_size', {
      value: Math.round(totalSize / 1024),
      event_category: 'Performance'
    })
  }, 1000)
}
```

---

## 🛠️ **PERFORMANCE TOOLS**

### **Development Tools**
```typescript
// Performance profiler for development
export const createPerformanceProfiler = () => {
  const marks = new Map<string, number>()
  
  return {
    start: (label: string) => {
      marks.set(label, performance.now())
    },
    
    end: (label: string) => {
      const startTime = marks.get(label)
      if (startTime) {
        const duration = performance.now() - startTime
        console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`)
        marks.delete(label)
        return duration
      }
    },
    
    measure: (label: string, fn: () => any) => {
      const start = performance.now()
      const result = fn()
      const duration = performance.now() - start
      console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`)
      return result
    }
  }
}

// Usage
const profiler = createPerformanceProfiler()

profiler.start('data-processing')
// ... expensive operation
profiler.end('data-processing')

const result = profiler.measure('api-call', () => {
  return fetch('/api/data').then(r => r.json())
})
```

### **Production Monitoring**
```typescript
// Real User Monitoring (RUM)
export const setupRUM = () => {
  // Track page load performance
  window.addEventListener('load', () => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    
    const metrics = {
      dns: navigation.domainLookupEnd - navigation.domainLookupStart,
      tcp: navigation.connectEnd - navigation.connectStart,
      ttfb: navigation.responseStart - navigation.requestStart,
      download: navigation.responseEnd - navigation.responseStart,
      domParse: navigation.domContentLoadedEventEnd - navigation.responseEnd,
      total: navigation.loadEventEnd - navigation.navigationStart
    }
    
    console.log('Page Load Metrics:', metrics)
    
    // Send to monitoring service
    fetch('/api/metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'page_load',
        metrics,
        url: window.location.href,
        userAgent: navigator.userAgent,
        timestamp: new Date().toISOString()
      })
    })
  })
  
  // Track resource loading
  new PerformanceObserver((list) => {
    list.getEntries().forEach(entry => {
      if (entry.duration > 1000) { // Log slow resources
        console.warn(`Slow resource: ${entry.name} (${entry.duration}ms)`)
      }
    })
  }).observe({ entryTypes: ['resource'] })
}
```

---

*"Performance is not a feature you add at the end - it's a fundamental quality that must be designed into every component, every interaction, and every line of code from the very beginning."*

**Next:** [Testing Guidelines](./TESTING_GUIDELINES.md) | [Deployment Guidelines](./DEPLOYMENT_GUIDELINES.md)
