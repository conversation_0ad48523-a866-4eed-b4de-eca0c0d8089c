# 🧪 TESTING GUIDELINES
## *Ensuring Cosmic Quality Through Comprehensive Testing*

---

## 🎯 **TESTING PHILOSOPHY**

### **Testing Pyramid for Cosmic Interfaces**
```
        /\
       /E2E\      ← 10% (Critical user journeys)
      /______\
     /        \
    /Integration\ ← 20% (Component interactions)
   /__________\
  /            \
 /    Unit      \ ← 70% (Pure functions, utilities)
/________________\
```

### **Golden Testing Principles**
1. **Test Behavior, Not Implementation** - Focus on what users experience
2. **Write Tests First** - TDD for critical functionality
3. **Fast Feedback Loops** - Tests should run quickly
4. **Reliable & Deterministic** - No flaky tests
5. **Maintainable** - Tests should be easy to update

---

## 🔬 **UNIT TESTING**

### **Testing Utilities & Pure Functions**
```typescript
// utils/date.test.ts
import { describe, it, expect } from 'vitest'
import { formatDate, isValidDate, addDays } from './date'

describe('Date Utilities', () => {
  describe('formatDate', () => {
    it('formats date in ISO format', () => {
      const date = new Date('2024-01-15')
      expect(formatDate(date)).toBe('2024-01-15')
    })
    
    it('handles invalid dates gracefully', () => {
      const invalidDate = new Date('invalid')
      expect(formatDate(invalidDate)).toBe('Invalid Date')
    })
  })
  
  describe('isValidDate', () => {
    it('returns true for valid dates', () => {
      expect(isValidDate(new Date())).toBe(true)
      expect(isValidDate(new Date('2024-01-15'))).toBe(true)
    })
    
    it('returns false for invalid dates', () => {
      expect(isValidDate(new Date('invalid'))).toBe(false)
      expect(isValidDate(null as any)).toBe(false)
    })
  })
  
  describe('addDays', () => {
    it('adds days correctly', () => {
      const date = new Date('2024-01-15')
      const result = addDays(date, 5)
      expect(formatDate(result)).toBe('2024-01-20')
    })
    
    it('handles negative days', () => {
      const date = new Date('2024-01-15')
      const result = addDays(date, -5)
      expect(formatDate(result)).toBe('2024-01-10')
    })
  })
})
```

### **Testing SolidJS Signals & Stores**
```typescript
// stores/customer.test.ts
import { describe, it, expect, beforeEach } from 'vitest'
import { createCustomerStore } from './customer'

describe('Customer Store', () => {
  let store: ReturnType<typeof createCustomerStore>
  
  beforeEach(() => {
    store = createCustomerStore()
  })
  
  it('initializes with empty customers', () => {
    expect(store.customers()).toEqual([])
    expect(store.loading()).toBe(false)
    expect(store.error()).toBeNull()
  })
  
  it('sets loading state when fetching customers', async () => {
    const fetchPromise = store.fetchCustomers()
    
    expect(store.loading()).toBe(true)
    
    await fetchPromise
    
    expect(store.loading()).toBe(false)
  })
  
  it('adds customer correctly', () => {
    const customer = { id: '1', name: 'John Doe', email: '<EMAIL>' }
    
    store.addCustomer(customer)
    
    expect(store.customers()).toContain(customer)
    expect(store.customers()).toHaveLength(1)
  })
  
  it('updates customer correctly', () => {
    const customer = { id: '1', name: 'John Doe', email: '<EMAIL>' }
    store.addCustomer(customer)
    
    const updatedCustomer = { ...customer, name: 'Jane Doe' }
    store.updateCustomer('1', updatedCustomer)
    
    expect(store.customers()[0].name).toBe('Jane Doe')
  })
  
  it('removes customer correctly', () => {
    const customer = { id: '1', name: 'John Doe', email: '<EMAIL>' }
    store.addCustomer(customer)
    
    store.removeCustomer('1')
    
    expect(store.customers()).toHaveLength(0)
  })
})
```

---

## 🧩 **COMPONENT TESTING**

### **Testing SolidJS Components**
```typescript
// components/GoldenButton.test.tsx
import { render, screen, fireEvent } from '@solidjs/testing-library'
import { describe, it, expect, vi } from 'vitest'
import { GoldenButton } from './GoldenButton'

describe('GoldenButton', () => {
  it('renders with correct text', () => {
    render(() => <GoldenButton>Click me</GoldenButton>)
    
    expect(screen.getByRole('button')).toHaveTextContent('Click me')
  })
  
  it('applies variant classes correctly', () => {
    render(() => <GoldenButton variant="cosmic">Test</GoldenButton>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveClass('from-cosmic-500')
  })
  
  it('applies size classes correctly', () => {
    render(() => <GoldenButton size="lg">Test</GoldenButton>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveClass('px-golden-lg')
  })
  
  it('handles click events', () => {
    const handleClick = vi.fn()
    render(() => <GoldenButton onClick={handleClick}>Click</GoldenButton>)
    
    fireEvent.click(screen.getByRole('button'))
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
  
  it('is disabled when disabled prop is true', () => {
    render(() => <GoldenButton disabled>Disabled</GoldenButton>)
    
    const button = screen.getByRole('button')
    expect(button).toBeDisabled()
    expect(button).toHaveClass('opacity-50')
  })
  
  it('shows glow effect when glow prop is true', () => {
    render(() => <GoldenButton glow variant="cosmic">Glow</GoldenButton>)
    
    const button = screen.getByRole('button')
    expect(button).toHaveClass('hover:shadow-[0_0_20px_rgba(14,165,233,0.5)]')
  })
  
  it('forwards ref correctly', () => {
    let buttonRef: HTMLButtonElement | undefined
    
    render(() => (
      <GoldenButton ref={buttonRef}>Test</GoldenButton>
    ))
    
    expect(buttonRef).toBeInstanceOf(HTMLButtonElement)
  })
})
```

### **Testing Complex Components with State**
```typescript
// components/CustomerForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@solidjs/testing-library'
import { describe, it, expect, vi } from 'vitest'
import { CustomerForm } from './CustomerForm'

describe('CustomerForm', () => {
  const mockOnSubmit = vi.fn()
  
  beforeEach(() => {
    mockOnSubmit.mockClear()
  })
  
  it('renders all form fields', () => {
    render(() => <CustomerForm onSubmit={mockOnSubmit} />)
    
    expect(screen.getByLabelText(/name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/phone/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /save/i })).toBeInTheDocument()
  })
  
  it('validates required fields', async () => {
    render(() => <CustomerForm onSubmit={mockOnSubmit} />)
    
    fireEvent.click(screen.getByRole('button', { name: /save/i }))
    
    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument()
      expect(screen.getByText(/email is required/i)).toBeInTheDocument()
    })
    
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })
  
  it('validates email format', async () => {
    render(() => <CustomerForm onSubmit={mockOnSubmit} />)
    
    fireEvent.input(screen.getByLabelText(/email/i), {
      target: { value: 'invalid-email' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /save/i }))
    
    await waitFor(() => {
      expect(screen.getByText(/invalid email format/i)).toBeInTheDocument()
    })
  })
  
  it('submits form with valid data', async () => {
    render(() => <CustomerForm onSubmit={mockOnSubmit} />)
    
    fireEvent.input(screen.getByLabelText(/name/i), {
      target: { value: 'John Doe' }
    })
    fireEvent.input(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    fireEvent.input(screen.getByLabelText(/phone/i), {
      target: { value: '+**********' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /save/i }))
    
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+**********'
      })
    })
  })
  
  it('shows loading state during submission', async () => {
    const slowSubmit = vi.fn(() => new Promise(resolve => setTimeout(resolve, 100)))
    render(() => <CustomerForm onSubmit={slowSubmit} />)
    
    // Fill form
    fireEvent.input(screen.getByLabelText(/name/i), {
      target: { value: 'John Doe' }
    })
    fireEvent.input(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    
    fireEvent.click(screen.getByRole('button', { name: /save/i }))
    
    expect(screen.getByRole('button', { name: /saving/i })).toBeDisabled()
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /save/i })).not.toBeDisabled()
    })
  })
})
```

---

## 🔗 **INTEGRATION TESTING**

### **Testing Component Interactions**
```typescript
// integration/CustomerManagement.test.tsx
import { render, screen, fireEvent, waitFor } from '@solidjs/testing-library'
import { describe, it, expect, vi } from 'vitest'
import { CustomerManagement } from '../pages/CustomerManagement'
import { createCustomerStore } from '../stores/customer'

// Mock API calls
vi.mock('../api/customers', () => ({
  fetchCustomers: vi.fn(() => Promise.resolve([
    { id: '1', name: 'John Doe', email: '<EMAIL>' },
    { id: '2', name: 'Jane Smith', email: '<EMAIL>' }
  ])),
  createCustomer: vi.fn((customer) => Promise.resolve({ id: '3', ...customer })),
  updateCustomer: vi.fn((id, customer) => Promise.resolve({ id, ...customer })),
  deleteCustomer: vi.fn(() => Promise.resolve())
}))

describe('Customer Management Integration', () => {
  it('loads and displays customers on mount', async () => {
    render(() => <CustomerManagement />)
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    })
  })
  
  it('creates new customer through form', async () => {
    render(() => <CustomerManagement />)
    
    // Open create form
    fireEvent.click(screen.getByRole('button', { name: /add customer/i }))
    
    // Fill form
    fireEvent.input(screen.getByLabelText(/name/i), {
      target: { value: 'New Customer' }
    })
    fireEvent.input(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' }
    })
    
    // Submit form
    fireEvent.click(screen.getByRole('button', { name: /save/i }))
    
    // Verify customer appears in list
    await waitFor(() => {
      expect(screen.getByText('New Customer')).toBeInTheDocument()
    })
  })
  
  it('filters customers by search term', async () => {
    render(() => <CustomerManagement />)
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('Jane Smith')).toBeInTheDocument()
    })
    
    // Search for John
    fireEvent.input(screen.getByPlaceholderText(/search customers/i), {
      target: { value: 'John' }
    })
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.queryByText('Jane Smith')).not.toBeInTheDocument()
    })
  })
  
  it('deletes customer with confirmation', async () => {
    render(() => <CustomerManagement />)
    
    await waitFor(() => {
      expect(screen.getByText('John Doe')).toBeInTheDocument()
    })
    
    // Click delete button
    fireEvent.click(screen.getByRole('button', { name: /delete john doe/i }))
    
    // Confirm deletion
    fireEvent.click(screen.getByRole('button', { name: /confirm delete/i }))
    
    // Verify customer is removed
    await waitFor(() => {
      expect(screen.queryByText('John Doe')).not.toBeInTheDocument()
    })
  })
})
```

---

## 🎭 **END-TO-END TESTING**

### **Playwright E2E Tests**
```typescript
// e2e/customer-management.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Customer Management', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/customers')
  })
  
  test('should display customer list', async ({ page }) => {
    await expect(page.getByRole('heading', { name: 'Customers' })).toBeVisible()
    await expect(page.getByRole('table')).toBeVisible()
  })
  
  test('should create new customer', async ({ page }) => {
    // Click add customer button
    await page.getByRole('button', { name: 'Add Customer' }).click()
    
    // Fill form
    await page.getByLabel('Name').fill('Test Customer')
    await page.getByLabel('Email').fill('<EMAIL>')
    await page.getByLabel('Phone').fill('+**********')
    
    // Submit form
    await page.getByRole('button', { name: 'Save' }).click()
    
    // Verify success message
    await expect(page.getByText('Customer created successfully')).toBeVisible()
    
    // Verify customer appears in list
    await expect(page.getByText('Test Customer')).toBeVisible()
  })
  
  test('should edit existing customer', async ({ page }) => {
    // Click edit button for first customer
    await page.getByRole('button', { name: /edit/i }).first().click()
    
    // Update name
    await page.getByLabel('Name').fill('Updated Customer')
    
    // Save changes
    await page.getByRole('button', { name: 'Save' }).click()
    
    // Verify update
    await expect(page.getByText('Customer updated successfully')).toBeVisible()
    await expect(page.getByText('Updated Customer')).toBeVisible()
  })
  
  test('should delete customer', async ({ page }) => {
    // Click delete button
    await page.getByRole('button', { name: /delete/i }).first().click()
    
    // Confirm deletion
    await page.getByRole('button', { name: 'Confirm Delete' }).click()
    
    // Verify deletion
    await expect(page.getByText('Customer deleted successfully')).toBeVisible()
  })
  
  test('should search customers', async ({ page }) => {
    // Type in search box
    await page.getByPlaceholder('Search customers...').fill('John')
    
    // Verify filtered results
    await expect(page.getByText('John Doe')).toBeVisible()
    await expect(page.getByText('Jane Smith')).not.toBeVisible()
  })
  
  test('should handle form validation', async ({ page }) => {
    // Click add customer
    await page.getByRole('button', { name: 'Add Customer' }).click()
    
    // Try to submit empty form
    await page.getByRole('button', { name: 'Save' }).click()
    
    // Verify validation errors
    await expect(page.getByText('Name is required')).toBeVisible()
    await expect(page.getByText('Email is required')).toBeVisible()
  })
})
```

### **Visual Regression Testing**
```typescript
// e2e/visual.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Visual Regression Tests', () => {
  test('customer list page', async ({ page }) => {
    await page.goto('/customers')
    await page.waitForLoadState('networkidle')
    
    await expect(page).toHaveScreenshot('customer-list.png')
  })
  
  test('customer form modal', async ({ page }) => {
    await page.goto('/customers')
    await page.getByRole('button', { name: 'Add Customer' }).click()
    
    await expect(page.getByRole('dialog')).toHaveScreenshot('customer-form-modal.png')
  })
  
  test('responsive design - mobile', async ({ page }) => {
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/customers')
    
    await expect(page).toHaveScreenshot('customer-list-mobile.png')
  })
  
  test('dark mode', async ({ page }) => {
    await page.goto('/customers')
    await page.getByRole('button', { name: 'Toggle Dark Mode' }).click()
    
    await expect(page).toHaveScreenshot('customer-list-dark.png')
  })
})
```

---

## 🔧 **TEST UTILITIES**

### **Custom Testing Utilities**
```typescript
// test-utils/render.tsx
import { render as solidRender } from '@solidjs/testing-library'
import { Router } from '@solidjs/router'
import { Component, JSX } from 'solid-js'

// Custom render with providers
export const render = (
  ui: () => JSX.Element,
  options: {
    route?: string
    initialEntries?: string[]
  } = {}
) => {
  const Wrapper: Component<{ children: JSX.Element }> = (props) => {
    return (
      <Router>
        {props.children}
      </Router>
    )
  }
  
  return solidRender(ui, {
    wrapper: Wrapper,
    ...options
  })
}

// Mock factories
export const createMockCustomer = (overrides = {}) => ({
  id: '1',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+**********',
  address: '123 Main St',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
})

export const createMockCustomers = (count = 3) => {
  return Array.from({ length: count }, (_, index) =>
    createMockCustomer({
      id: String(index + 1),
      name: `Customer ${index + 1}`,
      email: `customer${index + 1}@example.com`
    })
  )
}

// API mocking utilities
export const mockApiResponse = <T>(data: T, delay = 0) => {
  return new Promise<T>((resolve) => {
    setTimeout(() => resolve(data), delay)
  })
}

export const mockApiError = (message = 'API Error', status = 500) => {
  return Promise.reject(new Error(message))
}
```

### **Test Configuration**
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config'
import solid from 'vite-plugin-solid'

export default defineConfig({
  plugins: [solid()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test-utils/setup.ts'],
    globals: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test-utils/',
        '**/*.d.ts',
        '**/*.config.*',
        '**/coverage/**'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    }
  },
  resolve: {
    conditions: ['development', 'browser']
  }
})
```

---

## 📊 **TEST METRICS & REPORTING**

### **Coverage Requirements**
```json
{
  "coverage": {
    "global": {
      "branches": 80,
      "functions": 80,
      "lines": 80,
      "statements": 80
    },
    "perFile": {
      "branches": 70,
      "functions": 70,
      "lines": 70,
      "statements": 70
    }
  }
}
```

### **Test Performance Monitoring**
```typescript
// test-utils/performance.ts
export const measureTestPerformance = (testName: string, testFn: () => void) => {
  const start = performance.now()
  
  testFn()
  
  const end = performance.now()
  const duration = end - start
  
  if (duration > 1000) {
    console.warn(`Slow test detected: ${testName} took ${duration}ms`)
  }
  
  return duration
}

// Usage in tests
test('should render customer list', () => {
  measureTestPerformance('customer list render', () => {
    render(() => <CustomerList />)
    expect(screen.getByRole('table')).toBeInTheDocument()
  })
})
```

---

*"Testing is not about finding bugs - it's about building confidence. Every test we write is a promise to our users that our cosmic interface will work as expected, every time, in every situation."*

**Next:** [Deployment Guidelines](./DEPLOYMENT_GUIDELINES.md) | [API Documentation](../api/API_REFERENCE.md)
